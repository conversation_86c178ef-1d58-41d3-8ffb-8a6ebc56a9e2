import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取可用中间人列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const minAmount = parseFloat(searchParams.get('minAmount') || '0')
    const sortBy = searchParams.get('sortBy') || 'reputation' // reputation, feeRate, volume
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // 构建查询条件
    const where: any = {
      isMediator: true,
      mediatorStatus: 'ACTIVE',
      walletVerified: true
    }

    // 如果指定了最低金额，筛选保证金足够的中间人
    if (minAmount > 0) {
      // 计算每个中间人的可用保证金
      const mediators = await prisma.user.findMany({
        where,
        select: {
          id: true,
          mediatorDeposit: true
        }
      })

      const availableMediatorIds: string[] = []
      for (const mediator of mediators) {
        // 计算当前活跃订单占用的保证金
        const activeOrdersSum = await prisma.order.aggregate({
          where: {
            mediatorId: mediator.id,
            status: {
              in: ['PENDING', 'PAID', 'SHIPPED']
            },
            useEscrow: true
          },
          _sum: {
            totalAmount: true
          }
        })

        const usedDeposit = activeOrdersSum._sum.totalAmount || 0
        const availableDeposit = mediator.mediatorDeposit - usedDeposit

        if (availableDeposit >= minAmount) {
          availableMediatorIds.push(mediator.id)
        }
      }

      where.id = {
        in: availableMediatorIds
      }
    }

    // 构建排序条件
    let orderBy: any = { mediatorReputation: 'desc' }
    if (sortBy === 'feeRate') {
      orderBy = { mediatorFeeRate: 'asc' }
    } else if (sortBy === 'volume') {
      // 按交易量排序需要复杂查询，暂时使用信誉度
      orderBy = { mediatorReputation: 'desc' }
    }

      const mediators = await prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          avatar: true,
          mediatorFeeRate: true,
          mediatorDeposit: true,
          mediatorReputation: true,
          mediatorVerifiedAt: true,
          createdAt: true
        },
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    })

    // 为每个中间人添加统计信息
    const mediatorsWithStats = await Promise.all(
      mediators.map(async (mediator) => {
        // 获取交易统计
        const orderStats = await prisma.order.aggregate({
          where: {
            mediatorId: mediator.id,
            useEscrow: true
          },
          _count: {
            id: true
          },
          _sum: {
            totalAmount: true
          }
        })

        const completedOrders = await prisma.order.count({
          where: {
            mediatorId: mediator.id,
            useEscrow: true,
            status: 'COMPLETED'
          }
        })

        const activeOrders = await prisma.order.count({
          where: {
            mediatorId: mediator.id,
            useEscrow: true,
            status: {
              in: ['PENDING', 'PAID', 'SHIPPED']
            }
          }
        })

        // 计算可用保证金
        const activeOrdersSum = await prisma.order.aggregate({
          where: {
            mediatorId: mediator.id,
            status: {
              in: ['PENDING', 'PAID', 'SHIPPED']
            },
            useEscrow: true
          },
          _sum: {
            totalAmount: true
          }
        })

        const usedDeposit = activeOrdersSum._sum.totalAmount || 0
        const availableDeposit = mediator.mediatorDeposit - usedDeposit

        return {
          ...mediator,
          stats: {
            totalOrders: orderStats._count.id || 0,
            totalVolume: orderStats._sum.totalAmount || 0,
            completedOrders,
            activeOrders,
            successRate: orderStats._count.id > 0 ? (completedOrders / orderStats._count.id) * 100 : 0,
            availableDeposit
          }
        }
      })
    )

    const total = await prisma.user.count({ where })

    return NextResponse.json({
      success: true,
      data: {
        mediators: mediatorsWithStats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取中间人列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取中间人列表失败' },
      { status: 500 }
    )
  }
}

// 搜索中间人
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, filters } = body

    const where: any = {
      isMediator: true,
      mediatorStatus: 'ACTIVE',
      walletVerified: true
    }

    // 添加搜索条件
    if (query) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } }
      ]
    }

    // 添加筛选条件
    if (filters) {
      if (filters.minReputation) {
        where.mediatorReputation = { gte: filters.minReputation }
      }
      if (filters.maxFeeRate) {
        where.mediatorFeeRate = { lte: filters.maxFeeRate }
      }
      if (filters.minDeposit) {
        where.mediatorDeposit = { gte: filters.minDeposit }
      }
    }

    const mediators = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        avatar: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        mediatorReputation: true,
        mediatorVerifiedAt: true
      },
      orderBy: { mediatorReputation: 'desc' },
      take: 20
    })

    return NextResponse.json({
      success: true,
      data: mediators
    })
  } catch (error) {
    console.error('搜索中间人失败:', error)
    return NextResponse.json(
      { success: false, error: '搜索中间人失败' },
      { status: 500 }
    )
  }
}
