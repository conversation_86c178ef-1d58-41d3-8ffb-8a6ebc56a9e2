import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 审核提现申请
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { id: withdrawalId } = await params
    const { action, notes, txHash } = await request.json() // action: 'approve' | 'reject' | 'complete'

    if (!['approve', 'reject', 'complete'].includes(action)) {
      return NextResponse.json(
        { error: '无效的操作类型' },
        { status: 400 }
      )
    }

    // 获取提现记录
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true
          }
        }
      }
    })

    if (!withdrawal) {
      return NextResponse.json(
        { error: '提现记录不存在' },
        { status: 404 }
      )
    }

    if (action === 'approve') {
      // 批准提现申请
      if (withdrawal.status !== 'PENDING') {
        return NextResponse.json(
          { error: '该提现申请已处理' },
          { status: 400 }
        )
      }

      // 检查用户余额是否足够
      if (withdrawal.user.depositBalance < withdrawal.amount) {
        return NextResponse.json(
          { error: '用户余额不足' },
          { status: 400 }
        )
      }

      const result = await prisma.$transaction(async (tx) => {
        // 更新提现记录状态
        const updatedWithdrawal = await tx.withdrawal.update({
          where: { id: withdrawalId },
          data: {
            status: 'APPROVED',
            reviewedBy: admin.id,
            reviewedAt: new Date(),
            reviewNotes: notes
          }
        })

        // 冻结用户余额
        const updatedUser = await tx.user.update({
          where: { id: withdrawal.userId },
          data: {
            depositBalance: {
              decrement: withdrawal.amount
            }
          }
        })

        // 记录操作历史
        const operation = await tx.depositOperation.create({
          data: {
            userId: withdrawal.userId,
            operationType: 'WITHDRAWAL_APPROVE',
            amount: withdrawal.amount,
            balanceBefore: withdrawal.user.depositBalance,
            balanceAfter: withdrawal.user.depositBalance - withdrawal.amount,
            reason: '管理员批准提现申请',
            notes: notes || `批准提现申请 ${withdrawal.amount} USDT 到 ${withdrawal.walletAddress}`,
            operatorId: admin.id,
            relatedId: withdrawalId,
            relatedType: 'WITHDRAWAL'
          }
        })

        // 记录资金交易
        await tx.fundTransaction.create({
          data: {
            userId: withdrawal.userId,
            type: 'FREEZE',
            amount: -withdrawal.amount,
            description: `提现申请批准 - 冻结资金`,
            relatedId: withdrawalId,
            metadata: {
              withdrawalId: withdrawalId,
              walletAddress: withdrawal.walletAddress,
              approvedBy: admin.id,
              approvedAt: new Date().toISOString(),
              notes
            }
          }
        })

        return { updatedWithdrawal, updatedUser, operation }
      })

      return NextResponse.json({
        success: true,
        message: '提现申请已批准，资金已冻结',
        withdrawal: result.updatedWithdrawal,
        user: {
          id: result.updatedUser.id,
          name: result.updatedUser.name,
          email: result.updatedUser.email,
          depositBalance: result.updatedUser.depositBalance
        }
      })

    } else if (action === 'complete') {
      // 完成提现（已转账）
      if (withdrawal.status !== 'APPROVED') {
        return NextResponse.json(
          { error: '只能完成已批准的提现申请' },
          { status: 400 }
        )
      }

      if (!txHash) {
        return NextResponse.json(
          { error: '请提供交易哈希' },
          { status: 400 }
        )
      }

      const updatedWithdrawal = await prisma.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'COMPLETED',
          processedBy: admin.id,
          processedAt: new Date(),
          txHash: txHash
        }
      })

      // 记录操作历史
      await prisma.depositOperation.create({
        data: {
          userId: withdrawal.userId,
          operationType: 'WITHDRAWAL_COMPLETE',
          amount: 0,
          balanceBefore: withdrawal.user.depositBalance,
          balanceAfter: withdrawal.user.depositBalance,
          reason: '管理员完成提现转账',
          notes: `完成提现转账，交易哈希: ${txHash}`,
          operatorId: admin.id,
          relatedId: withdrawalId,
          relatedType: 'WITHDRAWAL'
        }
      })

      return NextResponse.json({
        success: true,
        message: '提现已完成',
        withdrawal: updatedWithdrawal
      })

    } else {
      // 拒绝提现申请
      if (!['PENDING', 'APPROVED'].includes(withdrawal.status)) {
        return NextResponse.json(
          { error: '该提现申请无法拒绝' },
          { status: 400 }
        )
      }

      const result = await prisma.$transaction(async (tx) => {
        // 更新提现记录状态
        const updatedWithdrawal = await tx.withdrawal.update({
          where: { id: withdrawalId },
          data: {
            status: 'REJECTED',
            reviewedBy: admin.id,
            reviewedAt: new Date(),
            reviewNotes: notes,
            cancelledBy: admin.id,
            cancelledAt: new Date(),
            cancelReason: notes || '管理员拒绝'
          }
        })

        let updatedUser: any = null
        // 如果之前已批准并冻结了资金，需要解冻
        if (withdrawal.status === 'APPROVED') {
          updatedUser = await tx.user.update({
            where: { id: withdrawal.userId },
            data: {
              depositBalance: {
                increment: withdrawal.amount
              }
            }
          })

          // 记录资金解冻
          await tx.fundTransaction.create({
            data: {
              userId: withdrawal.userId,
              type: 'UNFREEZE',
              amount: withdrawal.amount,
              description: `提现申请拒绝 - 解冻资金`,
              relatedId: withdrawalId,
              metadata: {
                withdrawalId: withdrawalId,
                rejectedBy: admin.id,
                rejectedAt: new Date().toISOString(),
                notes
              }
            }
          })
        }

        // 记录操作历史
        await tx.depositOperation.create({
          data: {
            userId: withdrawal.userId,
            operationType: 'WITHDRAWAL_REJECT',
            amount: 0,
            balanceBefore: withdrawal.user.depositBalance,
            balanceAfter: updatedUser?.depositBalance || withdrawal.user.depositBalance,
            reason: '管理员拒绝提现申请',
            notes: notes || `拒绝提现申请 ${withdrawal.amount} USDT`,
            operatorId: admin.id,
            relatedId: withdrawalId,
            relatedType: 'WITHDRAWAL'
          }
        })

        return { updatedWithdrawal, updatedUser }
      })

      return NextResponse.json({
        success: true,
        message: '提现申请已拒绝',
        withdrawal: result.updatedWithdrawal,
        user: result.updatedUser ? {
          id: result.updatedUser.id,
          name: result.updatedUser.name,
          email: result.updatedUser.email,
          depositBalance: result.updatedUser.depositBalance
        } : null
      })
    }

  } catch (error) {
    console.error('审核提现申请失败:', error)
    return NextResponse.json(
      { error: '审核失败' },
      { status: 500 }
    )
  }
}
