'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface ReportData {
  users?: {
    total: number
    new: number
    growth: string
  }
  products?: {
    total: number
    new: number
    growth: string
  }
  orders?: {
    total: number
    new: number
    growth: string
  }
  revenue?: {
    total: number
    new: number
    growth: string
  }
  dailyRevenue?: Array<{
    date: string
    revenue: number
    orders: number
  }>
  paymentStats?: Array<{
    status: string
    count: number
    totalAmount: number
    platformFee: number
  }>
  dailyUsers?: Array<{
    date: string
    newUsers: number
  }>
  roleStats?: Array<{
    role: string
    count: number
  }>
  activeUsers?: number
  dailyProducts?: Array<{
    date: string
    newProducts: number
  }>
  statusStats?: Array<{
    status: string
    count: number
  }>
  popularProducts?: Array<{
    id: string
    title: string
    price: number
    orderCount: number
    seller: {
      name: string | null
      email: string | null
    }
  }>
}

interface ReportsResponse {
  success: boolean
  period: number
  type: string
  data: ReportData
}

export default function AdminReports() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [reportData, setReportData] = useState<ReportData>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [period, setPeriod] = useState('30')
  const [reportType, setReportType] = useState('overview')

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, period, reportType])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        period,
        type: reportType
      })

      const response = await fetch(`/api/admin/reports?${params}`)
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: ReportsResponse = await response.json()
        setReportData(data.data)
      } else {
        setError('获取报表数据失败')
      }
    } catch (error) {
      console.error('财务报表页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} USDT`
  }

  const getRoleText = (role: string) => {
    const roleMap: { [key: string]: string } = {
      'USER': '普通用户',
      'ADMIN': '管理员',
      'GUARANTOR': '担保人'
    }
    return roleMap[role] || role
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'DRAFT': '草稿',
      'PENDING': '待审核',
      'PUBLISHED': '已发布',
      'REJECTED': '已拒绝',
      'SOLD_OUT': '已售完'
    }
    return statusMap[status] || status
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 财务报表
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和筛选 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">财务报表</h1>
            
            {/* 筛选控件 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-wrap gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">时间周期</label>
                  <select
                    value={period}
                    onChange={(e) => setPeriod(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="7">最近7天</option>
                    <option value="30">最近30天</option>
                    <option value="90">最近90天</option>
                    <option value="365">最近一年</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">报表类型</label>
                  <select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="overview">概览</option>
                    <option value="revenue">收入分析</option>
                    <option value="users">用户分析</option>
                    <option value="products">商品分析</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* 概览报表 */}
          {reportType === 'overview' && reportData.users && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">用</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">用户总数</dt>
                        <dd className="text-lg font-medium text-gray-900">{formatNumber(reportData.users.total)}</dd>
                        <dd className="text-sm text-green-600">新增 {formatNumber(reportData.users.new)} (+{reportData.users.growth}%)</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">商</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">商品总数</dt>
                        <dd className="text-lg font-medium text-gray-900">{formatNumber(reportData.products?.total || 0)}</dd>
                        <dd className="text-sm text-green-600">新增 {formatNumber(reportData.products?.new || 0)} (+{reportData.products?.growth || 0}%)</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">订</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">订单总数</dt>
                        <dd className="text-lg font-medium text-gray-900">{formatNumber(reportData.orders?.total || 0)}</dd>
                        <dd className="text-sm text-green-600">新增 {formatNumber(reportData.orders?.new || 0)} (+{reportData.orders?.growth || 0}%)</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">收</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">平台收入</dt>
                        <dd className="text-lg font-medium text-gray-900">{formatCurrency(reportData.revenue?.total || 0)}</dd>
                        <dd className="text-sm text-green-600">新增 {formatCurrency(reportData.revenue?.new || 0)} (+{reportData.revenue?.growth || 0}%)</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 收入分析报表 */}
          {reportType === 'revenue' && reportData.paymentStats && (
            <div className="space-y-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">支付状态统计</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {reportData.paymentStats.map((stat, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-500">
                          {stat.status === 'confirmed' ? '已确认支付' : '待确认支付'}
                        </span>
                        <span className="text-lg font-bold text-gray-900">
                          {formatNumber(stat.count)} 笔
                        </span>
                      </div>
                      <div className="mt-2 space-y-1">
                        <div className="text-sm text-gray-600">
                          交易总额: {formatCurrency(stat.totalAmount)}
                        </div>
                        <div className="text-sm text-gray-600">
                          平台手续费: {formatCurrency(stat.platformFee)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 用户分析报表 */}
          {reportType === 'users' && reportData.roleStats && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">用户角色分布</h3>
                  <div className="space-y-3">
                    {reportData.roleStats.map((stat, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">
                          {getRoleText(stat.role)}
                        </span>
                        <span className="text-lg font-bold text-gray-900">
                          {formatNumber(stat.count)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">用户活跃度</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">活跃用户</span>
                      <span className="text-lg font-bold text-green-600">
                        {formatNumber(reportData.activeUsers || 0)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      （有交易记录的用户）
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 商品分析报表 */}
          {reportType === 'products' && reportData.statusStats && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">商品状态分布</h3>
                  <div className="space-y-3">
                    {reportData.statusStats.map((stat, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">
                          {getStatusText(stat.status)}
                        </span>
                        <span className="text-lg font-bold text-gray-900">
                          {formatNumber(stat.count)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">热门商品 TOP 10</h3>
                  <div className="space-y-3">
                    {reportData.popularProducts?.slice(0, 5).map((product, index) => (
                      <div key={product.id} className="border-b pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {product.title}
                            </div>
                            <div className="text-xs text-gray-500">
                              {product.seller.name || product.seller.email}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-gray-900">
                              {formatNumber(product.orderCount)} 单
                            </div>
                            <div className="text-xs text-gray-500">
                              {formatCurrency(product.price)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
