'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  status: string
  paymentStatus: string
  totalAmount: number
  quantity: number
  platformFee: number
  paymentConfirmed: boolean
  createdAt: Date
  updatedAt: Date
  adminNotes: string | null
  paymentPin?: string
  paymentPinUsed: boolean
  paymentTxHash?: string
  product: {
    id: string
    title: string
    price: number
    status: string
  }
  buyer: {
    id: string
    userId: string | null
    name: string | null
    email: string | null
    creditScore: number
  }
  seller: {
    id: string
    userId: string | null
    name: string | null
    email: string | null
    creditScore: number
  }
}

interface OrdersResponse {
  orders: Order[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminOrdersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [pagination, setPagination] = useState<OrdersResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm, statusFilter, paymentStatusFilter])

  const checkAdminAccess = async () => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)
      if (paymentStatusFilter) params.append('paymentStatus', paymentStatusFilter)

      const response = await fetch(`/api/admin/orders?${params}`)

      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: OrdersResponse = await response.json()
        setOrders(data.orders)
        setPagination(data.pagination)
      } else {
        setError('获取订单列表失败')
      }
    } catch (error) {
      console.error('订单管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOrderAction = async (orderId: string, action: string) => {
    setActionLoading(orderId)
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      })

      if (response.ok) {
        alert('操作成功！')
        checkAdminAccess()
      } else {
        const data = await response.json()
        alert(data.error || '操作失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING_PAYMENT': '待付款',
      'PAID': '已付款',
      'SHIPPED': '已发货',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消',
      'REFUND_REQUESTED': '申请退款中'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING_PAYMENT': 'bg-yellow-100 text-yellow-800',
      'PAID': 'bg-blue-100 text-blue-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800',
      'REFUND_REQUESTED': 'bg-orange-100 text-orange-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                管理后台
              </Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-700">订单管理</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-gray-700 hover:text-gray-900">
                返回前台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
            <p className="mt-2 text-gray-600">管理和处理平台所有订单</p>
          </div>

          {/* 筛选器 */}
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  订单状态
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="PENDING_PAYMENT">待付款</option>
                  <option value="PAID">已付款</option>
                  <option value="SHIPPED">已发货</option>
                  <option value="COMPLETED">已完成</option>
                  <option value="CANCELLED">已取消</option>
                  <option value="REFUND_REQUESTED">申请退款中</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  搜索订单
                </label>
                <input
                  type="text"
                  placeholder="订单号、商品名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* 订单列表 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">暂无订单</div>
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {orders.map((order) => (
                  <li key={order.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-4">
                            <span className="text-sm font-medium text-gray-900">
                              {order.id}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                              {getStatusText(order.status)}
                            </span>
                            {order.status === 'PAID' && order.paymentStatus !== 'CONFIRMED' && (
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                待确认付款
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(order.createdAt).toLocaleString()}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-5">
                          <div>
                            <dt className="text-xs font-medium text-gray-500">商品</dt>
                            <dd className="text-sm text-gray-900">{order.product.title}</dd>
                          </div>
                          <div>
                            <dt className="text-xs font-medium text-gray-500">买家</dt>
                            <dd className="text-sm text-gray-900">
                              {order.buyer.name || order.buyer.email}
                            </dd>
                          </div>
                          <div>
                            <dt className="text-xs font-medium text-gray-500">卖家</dt>
                            <dd className="text-sm text-gray-900">
                              {order.seller.name || order.seller.email}
                            </dd>
                          </div>
                          <div>
                            <dt className="text-xs font-medium text-gray-500">金额</dt>
                            <dd className="text-sm font-medium text-gray-900">
                              {formatUSDT(order.totalAmount)}
                              <span className="text-xs text-gray-500 ml-1">
                                (手续费: {formatUSDT(order.platformFee)})
                              </span>
                            </dd>
                          </div>
                          <div>
                            <dt className="text-xs font-medium text-gray-500">PIN状态</dt>
                            <dd className="text-sm">
                              {order.paymentPin ? (
                                <div className="space-y-1">
                                  <span className="font-mono text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                    {order.paymentPin}
                                  </span>
                                  <div>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      order.paymentPinUsed
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-orange-100 text-orange-800'
                                    }`}>
                                      {order.paymentPinUsed ? '已使用' : '未使用'}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400 text-xs">无PIN码</span>
                              )}
                            </dd>
                          </div>
                        </div>

                        {order.paymentTxHash && (
                          <div className="mt-2">
                            <dt className="text-xs font-medium text-gray-500">交易哈希</dt>
                            <dd className="text-sm text-gray-900 font-mono break-all">
                              {order.paymentTxHash}
                            </dd>
                          </div>
                        )}
                      </div>

                      <div className="ml-6 flex space-x-2">
                        <Link
                          href={`/admin/orders/${order.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
                        >
                          查看详情
                        </Link>

                        {/* 管理员操作按钮 */}
                        {order.status === 'PAID' && !order.paymentConfirmed && (
                          <button
                            onClick={() => handleOrderAction(order.id, 'confirm_payment')}
                            disabled={actionLoading === order.id}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50"
                          >
                            {actionLoading === order.id ? '确认中...' : '确认付款'}
                          </button>
                        )}



                        {order.status === 'REFUND_REQUESTED' && (
                          <div className="flex space-x-1">
                            <button
                              onClick={() => handleOrderAction(order.id, 'approve_refund')}
                              disabled={actionLoading === order.id}
                              className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs font-medium disabled:opacity-50"
                            >
                              同意退款
                            </button>
                            <button
                              onClick={() => handleOrderAction(order.id, 'reject_refund')}
                              disabled={actionLoading === order.id}
                              className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs font-medium disabled:opacity-50"
                            >
                              拒绝退款
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
