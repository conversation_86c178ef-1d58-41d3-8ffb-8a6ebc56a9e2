'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface MediatorApplication {
  id: string
  userId: string
  bnbWalletAddress: string
  depositAmount: number
  feeRate: number
  experience: string
  introduction: string
  status: string
  createdAt: string
  approvedAt?: string
  rejectedAt?: string
  reviewNotes?: string
  user: {
    id: string
    name: string
    email: string
    creditScore: number
    depositBalance: number
    isMediator: boolean
    mediatorStatus?: string
  }
  reviewer?: {
    name: string
    email: string
  }
}

export default function MediatorManagement() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [applications, setApplications] = useState<MediatorApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL')
  const [selectedApp, setSelectedApp] = useState<MediatorApplication | null>(null)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve')
  const [reviewNotes, setReviewNotes] = useState('')
  const [reviewLoading, setReviewLoading] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/auth/signin')
      return
    }
    
    fetchApplications()
  }, [session, status, router, filter])

  const fetchApplications = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (filter !== 'ALL') {
        params.append('status', filter)
      }
      
      const response = await fetch(`/api/admin/mediator/applications?${params}`)
      if (response.ok) {
        const data = await response.json()
        setApplications(data.applications || [])
      }
    } catch (error) {
      console.error('获取申请列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReview = async () => {
    if (!selectedApp) return
    
    try {
      setReviewLoading(true)
      const response = await fetch('/api/admin/mediator/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          applicationId: selectedApp.id,
          action: reviewAction,
          reviewNotes
        })
      })

      if (response.ok) {
        alert(`申请已${reviewAction === 'approve' ? '批准' : '拒绝'}`)
        setShowReviewModal(false)
        setSelectedApp(null)
        setReviewNotes('')
        fetchApplications()
      } else {
        const error = await response.json()
        alert(error.error || '操作失败')
      }
    } catch (error) {
      alert('操作失败，请稍后重试')
    } finally {
      setReviewLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'PENDING': '待审核',
      'APPROVED': '已批准',
      'REJECTED': '已拒绝'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'APPROVED': 'bg-green-100 text-green-800',
      'REJECTED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              中间人管理
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              管理中间人申请和审核
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <Link
              href="/admin"
              className="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              返回管理后台
            </Link>
          </div>
        </div>

        {/* 筛选器 */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex space-x-4">
              {['ALL', 'PENDING', 'APPROVED', 'REJECTED'].map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status as any)}
                  className={`px-4 py-2 rounded-md text-sm font-medium ${
                    filter === status
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {status === 'ALL' ? '全部' : getStatusText(status)}
                  {status !== 'ALL' && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {applications.filter(app => app.status === status).length}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 申请列表 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              中间人申请列表
            </h3>
            
            {applications.length > 0 ? (
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        申请人
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        保证金
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        费率
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        信用分
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        申请时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {applications.map((app) => (
                      <tr key={app.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {app.user.name || '未设置'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {app.user.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {app.depositAmount.toFixed(2)} USDT
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {(app.feeRate * 100).toFixed(1)}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {app.user.creditScore}/100
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(app.status)}`}>
                            {getStatusText(app.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(app.createdAt).toLocaleDateString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setSelectedApp(app)
                              setShowReviewModal(true)
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            查看详情
                          </button>
                          {app.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => {
                                  setSelectedApp(app)
                                  setReviewAction('approve')
                                  setShowReviewModal(true)
                                }}
                                className="text-green-600 hover:text-green-900 mr-3"
                              >
                                批准
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedApp(app)
                                  setReviewAction('reject')
                                  setShowReviewModal(true)
                                }}
                                className="text-red-600 hover:text-red-900"
                              >
                                拒绝
                              </button>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">暂无申请记录</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 审核模态框 */}
      {showReviewModal && selectedApp && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {reviewAction === 'approve' ? '批准申请' : '拒绝申请'}
              </h3>
              
              {/* 申请详情 */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium text-gray-900 mb-2">申请详情</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">申请人:</span>
                    <span className="ml-2 font-medium">{selectedApp.user.name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">邮箱:</span>
                    <span className="ml-2">{selectedApp.user.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">保证金:</span>
                    <span className="ml-2 font-medium">{selectedApp.depositAmount} USDT</span>
                  </div>
                  <div>
                    <span className="text-gray-500">费率:</span>
                    <span className="ml-2 font-medium">{(selectedApp.feeRate * 100).toFixed(1)}%</span>
                  </div>
                  <div>
                    <span className="text-gray-500">信用分:</span>
                    <span className="ml-2 font-medium">{selectedApp.user.creditScore}/100</span>
                  </div>
                  <div>
                    <span className="text-gray-500">钱包地址:</span>
                    <span className="ml-2 text-xs">{selectedApp.bnbWalletAddress}</span>
                  </div>
                </div>
                
                <div className="mt-4">
                  <div className="text-gray-500 text-sm mb-1">申请理由:</div>
                  <div className="text-sm bg-white p-2 rounded border">
                    {selectedApp.introduction || '无'}
                  </div>
                </div>
                
                <div className="mt-2">
                  <div className="text-gray-500 text-sm mb-1">相关经验:</div>
                  <div className="text-sm bg-white p-2 rounded border">
                    {selectedApp.experience || '无'}
                  </div>
                </div>
              </div>

              {/* 审核意见 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  审核意见
                </label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入审核意见..."
                />
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowReviewModal(false)
                    setSelectedApp(null)
                    setReviewNotes('')
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleReview}
                  disabled={reviewLoading}
                  className={`px-4 py-2 rounded-md text-sm font-medium text-white ${
                    reviewAction === 'approve'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-red-600 hover:bg-red-700'
                  } disabled:opacity-50`}
                >
                  {reviewLoading ? '处理中...' : (reviewAction === 'approve' ? '批准申请' : '拒绝申请')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
