'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Dispute {
  id: string
  status: string
  totalAmount: number
  quantity: number
  createdAt: Date
  updatedAt: Date
  adminNotes: string | null
  product: {
    id: string
    title: string
    price: number
    images: string[]
  }
  buyer: {
    id: string
    userId: string | null
    name: string | null
    email: string | null
    creditScore: number
  }
  seller: {
    id: string
    userId: string | null
    name: string | null
    email: string | null
    creditScore: number
  }
}

interface DisputesResponse {
  disputes: Dispute[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminDisputes() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [disputes, setDisputes] = useState<Dispute[]>([])
  const [pagination, setPagination] = useState<DisputesResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })
      
      if (searchTerm) params.append('search', searchTerm)

      const response = await fetch(`/api/admin/disputes?${params}`)
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: DisputesResponse = await response.json()
        setDisputes(data.disputes)
        setPagination(data.pagination)
      } else {
        setError('获取纠纷列表失败')
      }
    } catch (error) {
      console.error('纠纷管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisputeAction = async (disputeId: string, action: string, data?: any) => {
    if (!confirm(`确定要执行此操作吗？`)) {
      return
    }

    setActionLoading(disputeId)
    try {
      const response = await fetch('/api/admin/disputes', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          disputeId,
          action,
          data
        })
      })

      if (response.ok) {
        // 刷新纠纷列表
        checkAdminAccess()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      console.error('纠纷操作错误:', error)
      alert('网络错误')
    } finally {
      setActionLoading(null)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    checkAdminAccess()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} USDT`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 纠纷处理
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和搜索 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">纠纷处理</h1>
            
            {/* 搜索 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <form onSubmit={handleSearch} className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="搜索订单ID、商品标题或用户..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  搜索
                </button>
              </form>
            </div>
          </div>

          {/* 统计信息 */}
          {pagination && (
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  共 {pagination.total} 个纠纷，第 {pagination.page} / {pagination.totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 纠纷列表 */}
          <div className="space-y-6">
            {disputes.map((dispute) => (
              <div key={dispute.id} className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 订单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">订单信息</h3>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">订单ID:</span> {dispute.id.slice(-8)}</div>
                      <div><span className="font-medium">商品:</span> {dispute.product.title}</div>
                      <div><span className="font-medium">数量:</span> {dispute.quantity}</div>
                      <div><span className="font-medium">总金额:</span> {formatCurrency(dispute.totalAmount)}</div>
                      <div><span className="font-medium">创建时间:</span> {formatDate(dispute.createdAt.toString())}</div>
                    </div>
                  </div>

                  {/* 当事人信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">当事人信息</h3>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm font-medium text-gray-700">买家</div>
                        <div className="text-sm text-gray-600">
                          {dispute.buyer.name || '未设置'} ({dispute.buyer.email})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {dispute.buyer.creditScore}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-700">卖家</div>
                        <div className="text-sm text-gray-600">
                          {dispute.seller.name || '未设置'} ({dispute.seller.email})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {dispute.seller.creditScore}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 处理操作 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">处理操作</h3>
                    <div className="space-y-2">
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'resolveForBuyer', { notes: '支持买家，同意退款' })}
                        disabled={actionLoading === dispute.id}
                        className="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                      >
                        {actionLoading === dispute.id ? '处理中...' : '支持买家'}
                      </button>
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'resolveForSeller', { notes: '支持卖家，维持交易' })}
                        disabled={actionLoading === dispute.id}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                      >
                        {actionLoading === dispute.id ? '处理中...' : '支持卖家'}
                      </button>
                      <button
                        onClick={() => {
                          const refundAmount = prompt('请输入部分退款金额 (USDT):', (dispute.totalAmount / 2).toString())
                          if (refundAmount && parseFloat(refundAmount) > 0) {
                            handleDisputeAction(dispute.id, 'partialRefund', { 
                              refundAmount: parseFloat(refundAmount),
                              notes: '管理员裁决部分退款'
                            })
                          }
                        }}
                        disabled={actionLoading === dispute.id}
                        className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                      >
                        {actionLoading === dispute.id ? '处理中...' : '部分退款'}
                      </button>
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'requestMoreInfo', { notes: '需要更多证据材料' })}
                        disabled={actionLoading === dispute.id}
                        className="w-full bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                      >
                        {actionLoading === dispute.id ? '处理中...' : '要求更多信息'}
                      </button>
                    </div>
                    
                    {/* 管理员备注 */}
                    <div className="mt-4">
                      <button
                        onClick={() => {
                          const notes = prompt('请输入管理员备注:', dispute.adminNotes || '')
                          if (notes !== null) {
                            handleDisputeAction(dispute.id, 'addNotes', { notes })
                          }
                        }}
                        className="w-full bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs"
                      >
                        添加备注
                      </button>
                      {dispute.adminNotes && (
                        <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-700">
                          <strong>备注:</strong> {dispute.adminNotes}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {disputes.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">暂无纠纷需要处理</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
