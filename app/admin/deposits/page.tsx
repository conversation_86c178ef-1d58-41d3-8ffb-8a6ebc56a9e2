'use client'

import React, { useState, useEffect } from 'react'
import { 
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  MinusIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon,
  UserIcon,
  DocumentTextIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'

interface User {
  id: string
  userId: string
  name: string
  email: string
  depositBalance: number
  creditScore: number
  status: string
  isGuarantor: boolean
  createdAt: string
  _count: {
    depositRecords: number
    fundTransactions: number
  }
}

interface DepositRecord {
  id: string
  amount: number
  method: string
  status: string
  txHash?: string
  paymentOrderId?: string
  pinCode?: string
  transactionHash?: string
  notes?: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    email: string
    depositBalance: number
    creditScore: number
  }
}

interface Withdrawal {
  id: string
  amount: number
  walletAddress: string
  status: string
  createdAt: string
  user: {
    id: string
    name: string
    email: string
    depositBalance: number
  }
}

interface DepositOperation {
  id: string
  operationType: string
  amount: number
  balanceBefore: number
  balanceAfter: number
  reason: string
  notes: string
  createdAt: string
  user: {
    id: string
    name: string
    email: string
  }
  operator: {
    id: string
    name: string
    email: string
  }
}

export default function DepositsManagementPage() {
  const [activeTab, setActiveTab] = useState('users')
  const [users, setUsers] = useState<User[]>([])
  const [deposits, setDeposits] = useState<DepositRecord[]>([])
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([])
  const [operations, setOperations] = useState<DepositOperation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 模态框状态
  const [showAdjustModal, setShowAdjustModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [adjustAmount, setAdjustAmount] = useState('')
  const [adjustType, setAdjustType] = useState<'MANUAL_ADD' | 'MANUAL_SUBTRACT'>('MANUAL_ADD')
  const [adjustReason, setAdjustReason] = useState('')
  const [adjustNotes, setAdjustNotes] = useState('')
  const [adjustLoading, setAdjustLoading] = useState(false)

  useEffect(() => {
    fetchData()
  }, [activeTab, pagination.page, searchTerm, statusFilter])

  const fetchData = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        type: activeTab,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
        status: statusFilter
      })

      const response = await fetch(`/api/admin/deposits?${params}`)
      if (response.ok) {
        const data = await response.json()
        
        if (activeTab === 'users') {
          setUsers(data.users || [])
        } else if (activeTab === 'deposits') {
          setDeposits(data.deposits || [])
        } else if (activeTab === 'withdrawals') {
          setWithdrawals(data.withdrawals || [])
        } else if (activeTab === 'operations') {
          setOperations(data.operations || [])
        }

        setPagination(data.pagination || pagination)
      }
    } catch (error) {
      console.error('获取数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAdjustBalance = async () => {
    if (!selectedUser || !adjustAmount || !adjustReason) {
      alert('请填写完整信息')
      return
    }

    setAdjustLoading(true)
    try {
      const response = await fetch('/api/admin/deposits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUser.id,
          operationType: adjustType,
          amount: parseFloat(adjustAmount),
          reason: adjustReason,
          notes: adjustNotes
        })
      })

      const result = await response.json()
      if (response.ok) {
        alert('保证金调整成功')
        setShowAdjustModal(false)
        setSelectedUser(null)
        setAdjustAmount('')
        setAdjustReason('')
        setAdjustNotes('')
        fetchData()
      } else {
        alert(result.error || '调整失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setAdjustLoading(false)
    }
  }

  const handleApproveDeposit = async (recordId: string, action: 'approve' | 'reject') => {
    const notes = prompt(`请输入${action === 'approve' ? '批准' : '拒绝'}原因:`)
    if (notes === null) return

    try {
      const response = await fetch(`/api/admin/deposits/${recordId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, notes })
      })

      const result = await response.json()
      if (response.ok) {
        alert(`充值申请已${action === 'approve' ? '批准' : '拒绝'}`)
        fetchData()
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  const handleApproveWithdrawal = async (withdrawalId: string, action: 'approve' | 'reject' | 'complete') => {
    let notes = ''
    let txHash = ''

    if (action === 'complete') {
      txHash = prompt('请输入交易哈希:') || ''
      if (!txHash) return
    } else {
      notes = prompt(`请输入${action === 'approve' ? '批准' : '拒绝'}原因:`) || ''
      if (!notes) return
    }

    try {
      const response = await fetch(`/api/admin/withdrawals/${withdrawalId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, notes, txHash })
      })

      const result = await response.json()
      if (response.ok) {
        alert(`提现申请已${action === 'approve' ? '批准' : action === 'reject' ? '拒绝' : '完成'}`)
        fetchData()
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <CurrencyDollarIcon className="h-8 w-8 text-green-600 mr-3" />
            用户保证金管理
          </h1>
          <p className="mt-2 text-gray-600">
            管理用户保证金余额、审核充值提现申请、查看操作历史
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'users', name: '用户余额', icon: UserIcon },
              { id: 'deposits', name: '充值申请', icon: PlusIcon },
              { id: 'withdrawals', name: '提现申请', icon: MinusIcon },
              { id: 'operations', name: '操作历史', icon: DocumentTextIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* 搜索和筛选 */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名、邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          {(activeTab === 'deposits' || activeTab === 'withdrawals') && (
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有状态</option>
              <option value="PENDING">待处理</option>
              <option value="APPROVED">已批准</option>
              <option value="COMPLETED">已完成</option>
              <option value="REJECTED">已拒绝</option>
              <option value="FAILED">失败</option>
            </select>
          )}
        </div>

        {/* 内容区域 */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : (
            <>
              {activeTab === 'users' && (
                <UsersTable 
                  users={users} 
                  onAdjustBalance={(user) => {
                    setSelectedUser(user)
                    setShowAdjustModal(true)
                  }}
                />
              )}
              
              {activeTab === 'deposits' && (
                <DepositsTable 
                  deposits={deposits} 
                  onApprove={handleApproveDeposit}
                />
              )}
              
              {activeTab === 'withdrawals' && (
                <WithdrawalsTable 
                  withdrawals={withdrawals} 
                  onApprove={handleApproveWithdrawal}
                />
              )}
              
              {activeTab === 'operations' && (
                <OperationsTable operations={operations} />
              )}
            </>
          )}
        </div>

        {/* 分页 */}
        {pagination.pages > 1 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
              共 {pagination.total} 条记录
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page <= 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page >= pagination.pages}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 调整保证金模态框 */}
      {showAdjustModal && selectedUser && (
        <AdjustBalanceModal
          user={selectedUser}
          adjustType={adjustType}
          setAdjustType={setAdjustType}
          adjustAmount={adjustAmount}
          setAdjustAmount={setAdjustAmount}
          adjustReason={adjustReason}
          setAdjustReason={setAdjustReason}
          adjustNotes={adjustNotes}
          setAdjustNotes={setAdjustNotes}
          loading={adjustLoading}
          onConfirm={handleAdjustBalance}
          onCancel={() => {
            setShowAdjustModal(false)
            setSelectedUser(null)
            setAdjustAmount('')
            setAdjustReason('')
            setAdjustNotes('')
          }}
        />
      )}
    </div>
  )
}

// 状态标识组件
const getStatusBadge = (status: string) => {
  const statusMap = {
    'PENDING': { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },
    'PENDING_APPROVAL': { color: 'bg-orange-100 text-orange-800', text: '等待确认' },
    'COMPLETED': { color: 'bg-green-100 text-green-800', text: '已完成' },
    'FAILED': { color: 'bg-red-100 text-red-800', text: '失败' },
    'APPROVED': { color: 'bg-blue-100 text-blue-800', text: '已批准' },
    'REJECTED': { color: 'bg-red-100 text-red-800', text: '已拒绝' },
    'ACTIVE': { color: 'bg-green-100 text-green-800', text: '活跃' },
    'SUSPENDED': { color: 'bg-red-100 text-red-800', text: '暂停' }
  }
  const config = statusMap[status as keyof typeof statusMap] || { color: 'bg-gray-100 text-gray-800', text: status }
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.text}
    </span>
  )
}

// 操作类型标识组件
const getOperationTypeBadge = (type: string) => {
  const typeMap = {
    'MANUAL_ADD': { color: 'bg-green-100 text-green-800', text: '手动增加' },
    'MANUAL_SUBTRACT': { color: 'bg-red-100 text-red-800', text: '手动减少' },
    'DEPOSIT_APPROVE': { color: 'bg-blue-100 text-blue-800', text: '充值批准' },
    'WITHDRAWAL_APPROVE': { color: 'bg-purple-100 text-purple-800', text: '提现批准' },
    'WITHDRAWAL_REJECT': { color: 'bg-red-100 text-red-800', text: '提现拒绝' },
    'WITHDRAWAL_COMPLETE': { color: 'bg-green-100 text-green-800', text: '提现完成' }
  }
  const config = typeMap[type as keyof typeof typeMap] || { color: 'bg-gray-100 text-gray-800', text: type }
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.text}
    </span>
  )
}

// 用户余额表格组件
function UsersTable({ users, onAdjustBalance }: {
  users: User[],
  onAdjustBalance: (user: User) => void
}) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              保证金余额
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              信用分数
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              交易记录
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {users.map((user) => (
            <tr key={user.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{user.name}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                  <div className="text-xs text-gray-400">ID: {user.userId}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {user.depositBalance.toFixed(2)} USDT
                </div>
                {user.isGuarantor && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    担保人
                  </span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {user.creditScore}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(user.status)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>充值: {user._count.depositRecords} 次</div>
                <div>交易: {user._count.fundTransactions} 次</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  onClick={() => onAdjustBalance(user)}
                  className="text-blue-600 hover:text-blue-900 flex items-center"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1" />
                  调整余额
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 充值申请表格组件
function DepositsTable({ deposits, onApprove }: {
  deposits: DepositRecord[],
  onApprove: (id: string, action: 'approve' | 'reject') => void
}) {
  const [expandedRow, setExpandedRow] = useState<string | null>(null)

  const toggleRowExpansion = (depositId: string) => {
    setExpandedRow(expandedRow === depositId ? null : depositId)
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              详情
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              订单信息
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              金额/支付方式
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              申请时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {deposits.map((deposit) => (
            <React.Fragment key={deposit.id}>
              <tr className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleRowExpansion(deposit.id)}
                    className="text-blue-600 hover:text-blue-900 flex items-center"
                  >
                    <span className="mr-1">
                      {expandedRow === deposit.id ? '▼' : '▶'}
                    </span>
                    详情
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {deposit.user.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {deposit.user.email}
                      </div>
                      <div className="text-xs text-gray-400">
                        ID: {deposit.user.id.slice(-8)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="font-medium">订单号: {deposit.id.slice(-8)}</div>
                    {deposit.transactionHash && (
                      <div className="text-xs text-gray-500 mt-1">
                        Hash: {deposit.transactionHash.slice(0, 10)}...
                      </div>
                    )}
                    {deposit.paymentOrderId && (
                      <div className="text-xs text-gray-500">
                        支付订单: {deposit.paymentOrderId.slice(-8)}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="font-medium">{deposit.amount} USDT</div>
                    <div className="text-xs text-gray-500">{deposit.method}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(deposit.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(deposit.createdAt).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {['PENDING', 'PENDING_APPROVAL'].includes(deposit.status) && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => onApprove(deposit.id, 'approve')}
                        className="bg-green-100 text-green-800 px-3 py-1 rounded-md hover:bg-green-200 transition-colors"
                      >
                        确认到账
                      </button>
                      <button
                        onClick={() => onApprove(deposit.id, 'reject')}
                        className="bg-red-100 text-red-800 px-3 py-1 rounded-md hover:bg-red-200 transition-colors"
                      >
                        拒绝申请
                      </button>
                    </div>
                  )}
                </td>
              </tr>
              {expandedRow === deposit.id && (
                <tr className="bg-gray-50">
                  <td colSpan={7} className="px-6 py-4">
                    <div className="bg-white rounded-lg p-4 shadow-sm border">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">充值详细信息</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {/* 订单信息 */}
                        <div className="space-y-3">
                          <h5 className="font-medium text-gray-700 border-b pb-1">订单信息</h5>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="text-gray-500">平台订单号:</span>
                              <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1">
                                {deposit.id}
                              </div>
                            </div>

                            {/* 根据支付方式显示不同信息 */}
                            {deposit.method === 'BINANCE_PAY' && (
                              <>
                                {deposit.paymentOrderId && (
                                  <div>
                                    <span className="text-gray-500">币安订单号:</span>
                                    <div className="font-mono text-xs bg-yellow-50 border border-yellow-200 p-2 rounded mt-1">
                                      {deposit.paymentOrderId}
                                    </div>
                                  </div>
                                )}
                                {deposit.pinCode && (
                                  <div>
                                    <span className="text-gray-500">PIN码:</span>
                                    <div className="font-mono text-lg font-bold text-blue-600 bg-blue-50 border border-blue-200 p-2 rounded mt-1 text-center">
                                      {deposit.pinCode}
                                    </div>
                                    <div className="text-xs text-gray-400 mt-1">
                                      请核实此PIN码与币安支付记录是否一致
                                    </div>
                                  </div>
                                )}
                              </>
                            )}

                            {deposit.method === 'BNB' && (
                              <>
                                {deposit.transactionHash && (
                                  <div>
                                    <span className="text-gray-500">BNB链交易Hash:</span>
                                    <div className="font-mono text-xs bg-green-50 border border-green-200 p-2 rounded mt-1 break-all">
                                      {deposit.transactionHash}
                                    </div>
                                    <div className="text-xs text-gray-400 mt-1">
                                      <a
                                        href={`https://bscscan.com/tx/${deposit.transactionHash}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500 hover:text-blue-700 underline"
                                      >
                                        在BSCScan上查看交易详情
                                      </a>
                                    </div>
                                  </div>
                                )}
                                {deposit.txHash && (
                                  <div>
                                    <span className="text-gray-500">交易Hash (备用):</span>
                                    <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1 break-all">
                                      {deposit.txHash}
                                    </div>
                                  </div>
                                )}
                              </>
                            )}

                            {deposit.method === 'USDT' && (
                              <>
                                {deposit.transactionHash && (
                                  <div>
                                    <span className="text-gray-500">USDT交易Hash:</span>
                                    <div className="font-mono text-xs bg-blue-50 border border-blue-200 p-2 rounded mt-1 break-all">
                                      {deposit.transactionHash}
                                    </div>
                                    <div className="text-xs text-gray-400 mt-1">
                                      <a
                                        href={`https://tronscan.org/#/transaction/${deposit.transactionHash}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500 hover:text-blue-700 underline"
                                      >
                                        在TronScan上查看交易详情
                                      </a>
                                    </div>
                                  </div>
                                )}
                                {deposit.txHash && (
                                  <div>
                                    <span className="text-gray-500">交易Hash (备用):</span>
                                    <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1 break-all">
                                      {deposit.txHash}
                                    </div>
                                  </div>
                                )}
                              </>
                            )}

                            {/* 其他支付方式的通用显示 */}
                            {!['BINANCE_PAY', 'BNB', 'USDT'].includes(deposit.method) && (
                              <>
                                {deposit.paymentOrderId && (
                                  <div>
                                    <span className="text-gray-500">支付订单号:</span>
                                    <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1">
                                      {deposit.paymentOrderId}
                                    </div>
                                  </div>
                                )}
                                {deposit.transactionHash && (
                                  <div>
                                    <span className="text-gray-500">交易Hash:</span>
                                    <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1 break-all">
                                      {deposit.transactionHash}
                                    </div>
                                  </div>
                                )}
                                {deposit.pinCode && (
                                  <div>
                                    <span className="text-gray-500">PIN码:</span>
                                    <div className="font-mono text-lg font-bold text-blue-600 bg-blue-50 p-2 rounded mt-1">
                                      {deposit.pinCode}
                                    </div>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>

                        {/* 用户信息 */}
                        <div className="space-y-3">
                          <h5 className="font-medium text-gray-700 border-b pb-1">用户信息</h5>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="text-gray-500">用户ID:</span>
                              <div className="font-mono text-xs">{deposit.user.id}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">用户名:</span>
                              <div>{deposit.user.name}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">邮箱:</span>
                              <div>{deposit.user.email}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">信用分数:</span>
                              <div className="font-medium">{deposit.user.creditScore}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">当前保证金:</span>
                              <div className="font-medium text-green-600">{deposit.user.depositBalance} USDT</div>
                            </div>
                          </div>
                        </div>

                        {/* 交易信息 */}
                        <div className="space-y-3">
                          <h5 className="font-medium text-gray-700 border-b pb-1">交易信息</h5>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="text-gray-500">充值金额:</span>
                              <div className="text-lg font-bold text-green-600">{deposit.amount} USDT</div>
                            </div>
                            <div>
                              <span className="text-gray-500">支付方式:</span>
                              <div>{deposit.method}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">申请时间:</span>
                              <div>{new Date(deposit.createdAt).toLocaleString()}</div>
                            </div>
                            {deposit.updatedAt !== deposit.createdAt && (
                              <div>
                                <span className="text-gray-500">更新时间:</span>
                                <div>{new Date(deposit.updatedAt).toLocaleString()}</div>
                              </div>
                            )}
                            <div>
                              <span className="text-gray-500">当前状态:</span>
                              <div className="mt-1">{getStatusBadge(deposit.status)}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 备注信息 */}
                      {deposit.notes && (
                        <div className="mt-4 pt-4 border-t">
                          <h5 className="font-medium text-gray-700 mb-2">备注信息</h5>
                          <div className="bg-yellow-50 border border-yellow-200 rounded p-3 text-sm">
                            {deposit.notes}
                          </div>
                        </div>
                      )}

                      {/* 操作按钮 */}
                      {deposit.status === 'PENDING' && (
                        <div className="mt-4 pt-4 border-t flex justify-end space-x-3">
                          <button
                            onClick={() => onApprove(deposit.id, 'reject')}
                            className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors"
                          >
                            拒绝充值
                          </button>
                          <button
                            onClick={() => onApprove(deposit.id, 'approve')}
                            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition-colors"
                          >
                            确认到账并批准
                          </button>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 提现申请表格组件
function WithdrawalsTable({ withdrawals, onApprove }: {
  withdrawals: Withdrawal[],
  onApprove: (id: string, action: 'approve' | 'reject' | 'complete') => void
}) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              提现金额
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              钱包地址
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              申请时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {withdrawals.map((withdrawal) => (
            <tr key={withdrawal.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{withdrawal.user.name}</div>
                  <div className="text-sm text-gray-500">{withdrawal.user.email}</div>
                  <div className="text-xs text-gray-400">
                    当前余额: {withdrawal.user.depositBalance.toFixed(2)} USDT
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {withdrawal.amount.toFixed(2)} USDT
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="max-w-xs truncate" title={withdrawal.walletAddress}>
                  {withdrawal.walletAddress}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(withdrawal.status)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(withdrawal.createdAt).toLocaleString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                {withdrawal.status === 'PENDING' && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onApprove(withdrawal.id, 'approve')}
                      className="text-green-600 hover:text-green-900 flex items-center"
                    >
                      <CheckIcon className="h-4 w-4 mr-1" />
                      批准
                    </button>
                    <button
                      onClick={() => onApprove(withdrawal.id, 'reject')}
                      className="text-red-600 hover:text-red-900 flex items-center"
                    >
                      <XMarkIcon className="h-4 w-4 mr-1" />
                      拒绝
                    </button>
                  </div>
                )}
                {withdrawal.status === 'APPROVED' && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onApprove(withdrawal.id, 'complete')}
                      className="text-blue-600 hover:text-blue-900 flex items-center"
                    >
                      <CheckIcon className="h-4 w-4 mr-1" />
                      完成转账
                    </button>
                    <button
                      onClick={() => onApprove(withdrawal.id, 'reject')}
                      className="text-red-600 hover:text-red-900 flex items-center"
                    >
                      <XMarkIcon className="h-4 w-4 mr-1" />
                      拒绝
                    </button>
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 操作历史表格组件
function OperationsTable({ operations }: { operations: DepositOperation[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作类型
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              金额变动
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              余额变化
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作原因
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作人
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作时间
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {operations.map((operation) => (
            <tr key={operation.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{operation.user.name}</div>
                  <div className="text-sm text-gray-500">{operation.user.email}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getOperationTypeBadge(operation.operationType)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span className={
                  operation.operationType.includes('ADD') || operation.operationType.includes('APPROVE')
                    ? 'text-green-600'
                    : 'text-red-600'
                }>
                  {operation.operationType.includes('ADD') || operation.operationType.includes('APPROVE') ? '+' : '-'}
                  {operation.amount.toFixed(2)} USDT
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>
                  前: {operation.balanceBefore.toFixed(2)} USDT
                </div>
                <div>
                  后: {operation.balanceAfter.toFixed(2)} USDT
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="text-sm text-gray-900">{operation.reason}</div>
                {operation.notes && (
                  <div className="text-xs text-gray-500 mt-1">{operation.notes}</div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{operation.operator.name}</div>
                  <div className="text-sm text-gray-500">{operation.operator.email}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(operation.createdAt).toLocaleString()}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 调整余额模态框组件
function AdjustBalanceModal({
  user,
  adjustType,
  setAdjustType,
  adjustAmount,
  setAdjustAmount,
  adjustReason,
  setAdjustReason,
  adjustNotes,
  setAdjustNotes,
  loading,
  onConfirm,
  onCancel
}: {
  user: User
  adjustType: 'MANUAL_ADD' | 'MANUAL_SUBTRACT'
  setAdjustType: (type: 'MANUAL_ADD' | 'MANUAL_SUBTRACT') => void
  adjustAmount: string
  setAdjustAmount: (amount: string) => void
  adjustReason: string
  setAdjustReason: (reason: string) => void
  adjustNotes: string
  setAdjustNotes: (notes: string) => void
  loading: boolean
  onConfirm: () => void
  onCancel: () => void
}) {
  const newBalance = adjustType === 'MANUAL_ADD'
    ? user.depositBalance + (parseFloat(adjustAmount) || 0)
    : user.depositBalance - (parseFloat(adjustAmount) || 0)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            调整保证金余额
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-4">
          {/* 用户信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">用户信息</div>
            <div className="text-lg font-medium text-gray-900">{user.name}</div>
            <div className="text-sm text-gray-500">{user.email}</div>
            <div className="text-sm text-gray-600 mt-2">
              当前余额: <span className="font-medium">{user.depositBalance.toFixed(2)} USDT</span>
            </div>
          </div>

          {/* 操作类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              操作类型
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="MANUAL_ADD"
                  checked={adjustType === 'MANUAL_ADD'}
                  onChange={(e) => setAdjustType(e.target.value as 'MANUAL_ADD')}
                  className="mr-2"
                />
                <PlusIcon className="h-4 w-4 text-green-500 mr-1" />
                增加余额
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="MANUAL_SUBTRACT"
                  checked={adjustType === 'MANUAL_SUBTRACT'}
                  onChange={(e) => setAdjustType(e.target.value as 'MANUAL_SUBTRACT')}
                  className="mr-2"
                />
                <MinusIcon className="h-4 w-4 text-red-500 mr-1" />
                减少余额
              </label>
            </div>
          </div>

          {/* 调整金额 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              调整金额 (USDT)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={adjustAmount}
              onChange={(e) => setAdjustAmount(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入调整金额"
            />
          </div>

          {/* 操作原因 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              操作原因 *
            </label>
            <input
              type="text"
              value={adjustReason}
              onChange={(e) => setAdjustReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入操作原因"
              required
            />
          </div>

          {/* 详细备注 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              详细备注
            </label>
            <textarea
              value={adjustNotes}
              onChange={(e) => setAdjustNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入详细备注（可选）"
            />
          </div>

          {/* 余额预览 */}
          {adjustAmount && (
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-sm text-blue-700">余额变化预览</div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-blue-600">操作前:</span>
                <span className="font-medium text-blue-900">{user.depositBalance.toFixed(2)} USDT</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-600">
                  {adjustType === 'MANUAL_ADD' ? '增加:' : '减少:'}
                </span>
                <span className={`font-medium ${adjustType === 'MANUAL_ADD' ? 'text-green-600' : 'text-red-600'}`}>
                  {adjustType === 'MANUAL_ADD' ? '+' : '-'}{parseFloat(adjustAmount).toFixed(2)} USDT
                </span>
              </div>
              <hr className="border-blue-200 my-2" />
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-600">操作后:</span>
                <span className={`font-bold ${newBalance >= 0 ? 'text-blue-900' : 'text-red-600'}`}>
                  {newBalance.toFixed(2)} USDT
                </span>
              </div>
              {newBalance < 0 && (
                <div className="text-xs text-red-600 mt-1">
                  ⚠️ 警告：操作后余额为负数
                </div>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-3 pt-4">
            <button
              onClick={onConfirm}
              disabled={loading || !adjustAmount || !adjustReason || newBalance < 0}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '处理中...' : '确认调整'}
            </button>
            <button
              onClick={onCancel}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
