'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { getUserStatusText } from '@/lib/user-status'

interface User {
  id: string
  userId: string | null
  name: string | null
  email: string | null
  role: string
  creditScore: number
  creditHistory: any[] | null
  // 中间人相关字段
  isMediator: boolean
  mediatorStatus: string
  emailVerified: Date | null
  status: string
  // 封禁相关字段
  bannedAt: Date | null
  bannedUntil: Date | null
  banReason: string | null
  bannedBy: string | null
  // 风险标记相关字段
  riskFlags: any[] | null
  riskLevel: string
  flaggedAt: Date | null
  flaggedBy: string | null
  flagNotes: string | null
  createdAt: Date
  updatedAt: Date
  _count: {
    products: number
    ordersAsBuyer: number
    ordersAsSeller: number
  }
}

interface UsersResponse {
  users: User[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminUsers() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [pagination, setPagination] = useState<UsersResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('')

  const [currentPage, setCurrentPage] = useState(1)
  const [showBanModal, setShowBanModal] = useState(false)
  const [showFlagModal, setShowFlagModal] = useState(false)
  const [showCreditModal, setShowCreditModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [banForm, setBanForm] = useState({
    reason: '',
    duration: 0 // 0表示永久封禁，其他数字表示天数
  })
  const [flagForm, setFlagForm] = useState({
    flagType: '',
    reason: '',
    riskLevel: 'MEDIUM'
  })
  const [creditForm, setCreditForm] = useState({
    newCreditScore: 0,
    reason: '',
    adjustmentType: 'SET' // SET, ADD, SUBTRACT
  })

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm, roleFilter])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (roleFilter) params.append('role', roleFilter)

      const response = await fetch(`/api/admin/users?${params}`, {
        // 添加缓存控制，确保获取最新数据
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: UsersResponse = await response.json()
        console.log('刷新用户列表，获取到用户数量:', data.users.length)
        console.log('用户状态:', data.users.map(u => ({ name: u.name, status: u.status, bannedUntil: u.bannedUntil })))
        setUsers(data.users)
        setPagination(data.pagination)
      } else {
        setError('获取用户列表失败')
      }
    } catch (error) {
      console.error('用户管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateUser = async (userId: string, action: string, data: any) => {
    try {
      console.log('发送请求:', { userId, action, data })

      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          action,
          data
        })
      })

      console.log('响应状态:', response.status)

      if (response.ok) {
        const result = await response.json()
        console.log('操作成功:', result)
        // 立即刷新用户列表以显示最新状态
        await checkAdminAccess()
      } else {
        const errorText = await response.text()
        console.error('API错误响应:', errorText)

        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || '更新失败')
        } catch (parseError) {
          throw new Error(`服务器错误 (${response.status}): ${errorText}`)
        }
      }
    } catch (error) {
      console.error('更新用户错误:', error)
      throw error // 重新抛出错误，让调用者处理
    }
  }

  const handleBanUser = (user: any) => {
    setSelectedUser(user)
    setBanForm({ reason: '', duration: 0 })
    setShowBanModal(true)
  }

  const handleConfirmBan = async () => {
    if (!selectedUser || !banForm.reason.trim()) {
      alert('请填写封禁原因')
      return
    }

    try {
      console.log('封禁用户:', selectedUser.name, '数据库ID:', selectedUser.id)

      // 执行封禁操作
      await handleUpdateUser(selectedUser.id, 'banUser', {
        banReason: banForm.reason,
        banDuration: banForm.duration
      })

      // 关闭模态框
      setShowBanModal(false)
      setSelectedUser(null)
      setBanForm({ reason: '', duration: 0 })

      // 显示成功消息
      alert('封禁成功！页面将自动刷新显示最新状态。')

      // 额外等待一下确保数据库操作完成，然后再次刷新
      setTimeout(async () => {
        await checkAdminAccess()
      }, 500)

    } catch (error) {
      console.error('封禁用户失败:', error)
      alert('封禁失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  const handleUnbanUser = async (userId: string) => {
    // 找到对应的用户信息
    const user = users.find(u => u.id === userId)
    const isEarlyUnban = user?.bannedUntil && new Date(user.bannedUntil) > new Date()

    const confirmMessage = isEarlyUnban
      ? '确定要提前解封此用户吗？用户将立即恢复正常使用权限。'
      : '确定要解封此用户吗？'

    if (confirm(confirmMessage)) {
      try {
        await handleUpdateUser(userId, 'unbanUser', {})
        alert('解封成功！页面将自动刷新显示最新状态。')
        // 额外刷新确保状态更新
        setTimeout(async () => {
          await checkAdminAccess()
        }, 500)
      } catch (error) {
        console.error('解封用户失败:', error)
        alert('解封失败: ' + (error instanceof Error ? error.message : '未知错误'))
      }
    }
  }

  const handleDeleteUser = async (userId: string, userName: string, userEmail: string) => {
    // 第一次确认 - 基本警告
    const firstConfirm = confirm(
      `⚠️ 危险操作警告 ⚠️\n\n` +
      `您即将永久删除用户: "${userName}" (${userEmail})\n\n` +
      `这是不可逆的删除操作，将会：\n` +
      `• 完全从数据库中移除用户记录\n` +
      `• 删除用户的所有个人数据\n` +
      `• 删除用户的地址、消息、收藏等信息\n` +
      `• 该邮箱和用户名将可以重新注册使用\n\n` +
      `此操作不可撤销！确定要继续吗？`
    )

    if (!firstConfirm) {
      return
    }

    // 第二次确认 - 输入确认
    const confirmText = prompt(
      `最终确认删除操作\n\n` +
      `请输入用户邮箱 "${userEmail}" 来确认删除：`
    )

    if (confirmText !== userEmail) {
      alert('邮箱输入不匹配，删除操作已取消。')
      return
    }

    try {
      const response = await handleUpdateUser(userId, 'deleteUser', {})

      // 显示成功信息
      alert(
        `✅ 用户删除成功！\n\n` +
        `用户 "${userName}" 已被永久删除\n` +
        `邮箱 "${userEmail}" 现在可以重新注册\n\n` +
        `页面将自动刷新显示最新状态。`
      )

      // 刷新页面数据
      setTimeout(async () => {
        await checkAdminAccess()
      }, 500)

    } catch (error) {
      console.error('删除用户失败:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      alert(
        `❌ 删除失败\n\n` +
        `错误信息: ${errorMessage}\n\n` +
        `可能的原因：\n` +
        `• 用户有未完成的订单\n` +
        `• 数据库约束冲突\n` +
        `• 网络连接问题\n\n` +
        `请检查后重试。`
      )
    }
  }

  // 标记用户为风险用户
  const handleFlagUser = (user: any) => {
    setSelectedUser(user)
    setFlagForm({ flagType: '', reason: '', riskLevel: 'MEDIUM' })
    setShowFlagModal(true)
  }

  // 确认标记用户
  const handleConfirmFlag = async () => {
    if (!selectedUser || !flagForm.flagType.trim() || !flagForm.reason.trim()) {
      alert('请填写标记类型和原因')
      return
    }

    try {
      await handleUpdateUser(selectedUser.id, 'flagUser', {
        flagType: flagForm.flagType,
        reason: flagForm.reason,
        riskLevel: flagForm.riskLevel
      })

      setShowFlagModal(false)
      setSelectedUser(null)
      setFlagForm({ flagType: '', reason: '', riskLevel: 'MEDIUM' })

      alert('用户标记成功！')
      setTimeout(async () => {
        await checkAdminAccess()
      }, 500)

    } catch (error) {
      console.error('标记用户失败:', error)
      alert('标记失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 移除用户标记
  const handleRemoveFlag = async (userId: string, userName: string) => {
    if (confirm(`确定要移除用户 "${userName}" 的风险标记吗？`)) {
      try {
        await handleUpdateUser(userId, 'removeFlag', {})
        alert('标记移除成功！')
        setTimeout(async () => {
          await checkAdminAccess()
        }, 500)
      } catch (error) {
        console.error('移除标记失败:', error)
        alert('移除标记失败: ' + (error instanceof Error ? error.message : '未知错误'))
      }
    }
  }

  // 修改用户信用分数
  const handleCreditScore = (user: any) => {
    setSelectedUser(user)
    setCreditForm({
      newCreditScore: user.creditScore,
      reason: '',
      adjustmentType: 'SET'
    })
    setShowCreditModal(true)
  }

  // 确认修改信用分数
  const handleConfirmCredit = async () => {
    if (!selectedUser || !creditForm.reason.trim()) {
      alert('请填写修改原因')
      return
    }

    // 验证信用分数范围
    let finalScore = creditForm.newCreditScore
    if (creditForm.adjustmentType === 'ADD') {
      finalScore = selectedUser.creditScore + creditForm.newCreditScore
    } else if (creditForm.adjustmentType === 'SUBTRACT') {
      finalScore = selectedUser.creditScore - creditForm.newCreditScore
    }

    if (finalScore < 0 || finalScore > 1000) {
      alert('信用分数必须在0-1000之间')
      return
    }

    try {
      await handleUpdateUser(selectedUser.id, 'updateCreditScore', {
        creditScore: finalScore,
        reason: creditForm.reason,
        adjustmentType: creditForm.adjustmentType,
        originalScore: selectedUser.creditScore
      })

      setShowCreditModal(false)
      setSelectedUser(null)
      setCreditForm({ newCreditScore: 0, reason: '', adjustmentType: 'SET' })

      alert(`信用分数修改成功！新分数: ${finalScore}`)
      setTimeout(async () => {
        await checkAdminAccess()
      }, 500)

    } catch (error) {
      console.error('修改信用分数失败:', error)
      alert('修改失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    checkAdminAccess()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getRoleText = (role: string) => {
    return role === 'ADMIN' ? '管理员' : '普通用户'
  }

  const getRoleBadgeColor = (role: string) => {
    return role === 'ADMIN'
      ? 'bg-purple-100 text-purple-800'
      : 'bg-blue-100 text-blue-800'
  }

  const getRiskLevelText = (riskLevel: string) => {
    const riskMap: Record<string, string> = {
      'NORMAL': '正常',
      'LOW': '低风险',
      'MEDIUM': '中风险',
      'HIGH': '高风险',
      'CRITICAL': '极高风险'
    }
    return riskMap[riskLevel] || riskLevel
  }

  const getRiskLevelColor = (riskLevel: string) => {
    const colorMap: Record<string, string> = {
      'NORMAL': 'bg-green-100 text-green-800',
      'LOW': 'bg-yellow-100 text-yellow-800',
      'MEDIUM': 'bg-orange-100 text-orange-800',
      'HIGH': 'bg-red-100 text-red-800',
      'CRITICAL': 'bg-red-200 text-red-900'
    }
    return colorMap[riskLevel] || 'bg-gray-100 text-gray-800'
  }

  const formatRiskFlags = (riskFlags: any[] | null) => {
    if (!riskFlags || riskFlags.length === 0) return null

    return riskFlags.map((flag, index) => (
      <div key={index} className="text-xs text-red-600 mt-1">
        {flag.type}: {flag.reason}
      </div>
    ))
  }

  // 获取用户状态颜色
  const getUserStatusColor = (status: string) => {
    return status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
           status === 'BANNED' ? 'bg-red-100 text-red-800' :
           status === 'SUSPENDED' ? 'bg-yellow-100 text-yellow-800' :
           'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-3">
              <Link href="/admin" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
              <span className="text-xl font-bold text-gray-900">用户管理</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和搜索 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">用户管理</h1>
            
            {/* 搜索和筛选 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="搜索用户名、邮箱或用户ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有角色</option>
                    <option value="USER">普通用户</option>
                    <option value="ADMIN">管理员</option>
                  </select>
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  搜索
                </button>
              </form>
            </div>
          </div>

          {/* 统计信息 */}
          {pagination && (
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  共 {pagination.total} 个用户，第 {pagination.page} / {pagination.totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 用户列表 */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      角色
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      风险标记
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      信用分数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      统计
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      注册时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {user.name || '未设置'}
                            </div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                            <div className="text-xs text-gray-400">{user.userId}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user.role)}`}>
                          {getRoleText(user.role)}
                        </span>
                        {user.isMediator && (
                          <span className="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                            中间人
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserStatusColor(user.status)}`}>
                          {getUserStatusText(user.status)}
                        </span>
                        {user.status === 'BANNED' && user.bannedUntil && (
                          <div className="text-xs text-gray-500 mt-1">
                            至 {new Date(user.bannedUntil).toLocaleDateString('zh-CN')}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {user.riskLevel !== 'NORMAL' ? (
                          <div>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(user.riskLevel)}`}>
                              {getRiskLevelText(user.riskLevel)}
                            </span>
                            {user.flaggedAt && (
                              <div className="text-xs text-gray-500 mt-1">
                                标记于 {new Date(user.flaggedAt).toLocaleDateString('zh-CN')}
                              </div>
                            )}
                            {formatRiskFlags(user.riskFlags)}
                          </div>
                        ) : (
                          <span className="text-xs text-gray-500">无标记</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-semibold">
                          {user.creditScore}
                        </div>
                        {user.creditHistory && user.creditHistory.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            最近修改: {new Date(user.creditHistory[user.creditHistory.length - 1].adjustedAt).toLocaleDateString('zh-CN')}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>商品: {user._count.products}</div>
                        <div>买单: {user._count.ordersAsBuyer}</div>
                        <div>卖单: {user._count.ordersAsSeller}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(user.createdAt.toString())}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-col space-y-1">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleUpdateUser(user.id, 'updateRole', {
                                  role: user.role === 'ADMIN' ? 'USER' : 'ADMIN'
                                })}
                                className="text-blue-600 hover:text-blue-900 text-xs"
                              >
                                {user.role === 'ADMIN' ? '取消管理员' : '设为管理员'}
                              </button>
                              <button
                                onClick={() => handleUpdateUser(user.id, 'updateMediator', {
                                  isMediator: !user.isMediator,
                                  mediatorStatus: !user.isMediator ? 'ACTIVE' : 'INACTIVE'
                                })}
                                className="text-orange-600 hover:text-orange-900 text-xs"
                              >
                                {user.isMediator ? '取消中间人' : '设为中间人'}
                              </button>
                              <button
                                onClick={() => handleCreditScore(user)}
                                className="text-purple-600 hover:text-purple-900 text-xs"
                              >
                                💳 修改信用
                              </button>
                            </div>
                            <div className="flex space-x-2">
                              {user.status === 'BANNED' ? (
                                <button
                                  onClick={() => handleUnbanUser(user.id)}
                                  className="text-green-600 hover:text-green-900 text-xs"
                                >
                                  {user.bannedUntil ? '提前解封' : '解封'}
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleBanUser(user)}
                                  className="text-red-600 hover:text-red-900 text-xs"
                                >
                                  封禁
                                </button>
                              )}
                              <button
                                onClick={() => handleDeleteUser(user.id, user.name || user.email || '未知用户', user.email || '未知邮箱')}
                                className="text-red-800 hover:text-red-900 text-xs"
                              >
                                🗑️ 永久删除
                              </button>
                            </div>
                            <div className="flex space-x-2 mt-1">
                              {user.riskLevel !== 'NORMAL' ? (
                                <button
                                  onClick={() => handleRemoveFlag(user.id, user.name || user.email || '未知用户')}
                                  className="text-blue-600 hover:text-blue-900 text-xs"
                                >
                                  🏳️ 移除标记
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleFlagUser(user)}
                                  className="text-orange-600 hover:text-orange-900 text-xs"
                                >
                                  🚩 标记风险
                                </button>
                              )}
                            </div>
                          </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* 封禁用户模态框 */}
      {showBanModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                封禁用户: {selectedUser?.name || selectedUser?.email}
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  封禁原因 *
                </label>
                <textarea
                  value={banForm.reason}
                  onChange={(e) => setBanForm(prev => ({ ...prev, reason: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="请输入封禁原因..."
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  封禁时长
                </label>
                <select
                  value={banForm.duration}
                  onChange={(e) => setBanForm(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={0}>永久封禁</option>
                  <option value={1}>1天</option>
                  <option value={3}>3天</option>
                  <option value={7}>7天</option>
                  <option value={30}>30天</option>
                  <option value={90}>90天</option>
                </select>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowBanModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmBan}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md"
                >
                  确认封禁
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 标记用户模态框 */}
      {showFlagModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                🚩 标记风险用户: {selectedUser?.name || selectedUser?.email}
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标记类型 *
                </label>
                <select
                  value={flagForm.flagType}
                  onChange={(e) => setFlagForm(prev => ({ ...prev, flagType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择标记类型</option>
                  <option value="FRAUD_RISK">欺诈风险</option>
                  <option value="PAYMENT_ISSUE">支付问题</option>
                  <option value="FAKE_GOODS">假货嫌疑</option>
                  <option value="DISPUTE_FREQUENT">频繁纠纷</option>
                  <option value="SUSPICIOUS_BEHAVIOR">可疑行为</option>
                  <option value="IDENTITY_ISSUE">身份问题</option>
                  <option value="COMMUNICATION_ISSUE">沟通问题</option>
                  <option value="OTHER">其他</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  风险等级 *
                </label>
                <select
                  value={flagForm.riskLevel}
                  onChange={(e) => setFlagForm(prev => ({ ...prev, riskLevel: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="LOW">低风险</option>
                  <option value="MEDIUM">中风险</option>
                  <option value="HIGH">高风险</option>
                  <option value="CRITICAL">极高风险</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标记原因 *
                </label>
                <textarea
                  value={flagForm.reason}
                  onChange={(e) => setFlagForm(prev => ({ ...prev, reason: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="请详细描述标记原因..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowFlagModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmFlag}
                  className="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md"
                >
                  🚩 确认标记
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 修改信用分数模态框 */}
      {showCreditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                💳 修改信用分数: {selectedUser?.name || selectedUser?.email}
              </h3>

              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">
                  当前信用分数: <span className="font-semibold text-blue-600">{selectedUser?.creditScore}</span>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  操作类型 *
                </label>
                <select
                  value={creditForm.adjustmentType}
                  onChange={(e) => setCreditForm(prev => ({ ...prev, adjustmentType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="SET">设置为指定值</option>
                  <option value="ADD">增加分数</option>
                  <option value="SUBTRACT">减少分数</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {creditForm.adjustmentType === 'SET' ? '新信用分数' :
                   creditForm.adjustmentType === 'ADD' ? '增加分数' : '减少分数'} *
                </label>
                <input
                  type="number"
                  min="0"
                  max={creditForm.adjustmentType === 'SET' ? '1000' : '500'}
                  value={creditForm.newCreditScore}
                  onChange={(e) => setCreditForm(prev => ({ ...prev, newCreditScore: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={creditForm.adjustmentType === 'SET' ? '0-1000' : '输入调整数值'}
                />
                {creditForm.adjustmentType !== 'SET' && (
                  <div className="text-xs text-gray-500 mt-1">
                    预计结果: {
                      creditForm.adjustmentType === 'ADD'
                        ? selectedUser?.creditScore + creditForm.newCreditScore
                        : selectedUser?.creditScore - creditForm.newCreditScore
                    }
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  修改原因 *
                </label>
                <textarea
                  value={creditForm.reason}
                  onChange={(e) => setCreditForm(prev => ({ ...prev, reason: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="请详细说明修改信用分数的原因..."
                />
              </div>

              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="text-xs text-yellow-800">
                  <strong>注意事项：</strong>
                  <ul className="mt-1 list-disc list-inside">
                    <li>信用分数范围：0-1000</li>
                    <li>此操作将被记录到用户信用历史中</li>
                    <li>请谨慎操作，确保有充分理由</li>
                  </ul>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowCreditModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmCredit}
                  className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
                >
                  💳 确认修改
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
