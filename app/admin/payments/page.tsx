'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Payment {
  id: string
  amount: number
  paymentMethod: string
  status: string
  txHash: string | null
  refundAmount: number | null
  refundTxHash: string | null
  adminNotes: string | null
  createdAt: Date
  confirmedAt: Date | null
  refundedAt: Date | null
  order: {
    id: string
    status: string
    product: {
      id: string
      title: string
      price: number
    }
    buyer: {
      id: string
      userId: string | null
      name: string | null
      email: string | null
      creditScore: number
    }
    seller: {
      id: string
      userId: string | null
      name: string | null
      email: string | null
      creditScore: number
    }
  }
}

interface PaymentsResponse {
  payments: Payment[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminPayments() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [payments, setPayments] = useState<Payment[]>([])
  const [pagination, setPagination] = useState<PaymentsResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm, statusFilter, paymentMethodFilter])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })
      
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)
      if (paymentMethodFilter) params.append('paymentMethod', paymentMethodFilter)

      const response = await fetch(`/api/admin/payments?${params}`)
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: PaymentsResponse = await response.json()
        setPayments(data.payments)
        setPagination(data.pagination)
      } else {
        setError('获取支付列表失败')
      }
    } catch (error) {
      console.error('支付管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePaymentAction = async (paymentId: string, action: string, data?: any) => {
    if (!confirm(`确定要执行此操作吗？`)) {
      return
    }

    setActionLoading(paymentId)
    try {
      const response = await fetch('/api/admin/payments', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          paymentId,
          action,
          data
        })
      })

      if (response.ok) {
        const successData = await response.json()
        // 显示成功消息
        alert(successData.message || '操作成功！')

        // 添加短暂延迟后刷新支付列表，确保数据库更新完成
        setTimeout(() => {
          checkAdminAccess()
        }, 500)
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      console.error('支付操作错误:', error)
      alert('网络错误')
    } finally {
      setActionLoading(null)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    checkAdminAccess()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'PENDING': '待确认',
      'CONFIRMED': '已确认',
      'REJECTED': '已拒绝',
      'REFUNDED': '已退款'
    }
    return statusMap[status] || status
  }

  const getStatusBadgeColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'CONFIRMED': 'bg-green-100 text-green-800',
      'REJECTED': 'bg-red-100 text-red-800',
      'REFUNDED': 'bg-blue-100 text-blue-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getPaymentMethodText = (method: string) => {
    const methodMap: { [key: string]: string } = {
      'USDT_TRC20': 'USDT (TRC20)',
      'USDT_ERC20': 'USDT (ERC20)',
      'BANK_TRANSFER': '银行转账',
      'ALIPAY': '支付宝',
      'WECHAT': '微信支付'
    }
    return methodMap[method] || method
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 支付管理
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和搜索 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">支付管理</h1>
            
            {/* 搜索和筛选 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="搜索支付ID、交易哈希或用户..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有状态</option>
                    <option value="PENDING">待确认</option>
                    <option value="CONFIRMED">已确认</option>
                    <option value="REJECTED">已拒绝</option>
                    <option value="REFUNDED">已退款</option>
                  </select>
                </div>
                <div>
                  <select
                    value={paymentMethodFilter}
                    onChange={(e) => setPaymentMethodFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有支付方式</option>
                    <option value="USDT_TRC20">USDT (TRC20)</option>
                    <option value="USDT_ERC20">USDT (ERC20)</option>
                    <option value="BANK_TRANSFER">银行转账</option>
                    <option value="ALIPAY">支付宝</option>
                    <option value="WECHAT">微信支付</option>
                  </select>
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  搜索
                </button>
              </form>
            </div>
          </div>

          {/* 统计信息 */}
          {pagination && (
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  共 {pagination.total} 个支付记录，第 {pagination.page} / {pagination.totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 支付列表 */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      支付信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      订单信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      买家/卖家
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment) => (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {payment.amount} USDT
                          </div>
                          <div className="text-sm text-gray-500">
                            {getPaymentMethodText(payment.paymentMethod)}
                          </div>
                          {payment.txHash && (
                            <div className="text-xs text-gray-400 truncate max-w-32">
                              {payment.txHash}
                            </div>
                          )}
                          {payment.refundAmount && (
                            <div className="text-xs text-red-600">
                              退款: {payment.refundAmount} USDT
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900 truncate max-w-32">
                            {payment.order.product.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            订单: {payment.order.id.slice(-8)}
                          </div>
                          <div className="text-xs text-gray-400">
                            商品价格: {payment.order.product.price} USDT
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div>
                            <span className="text-xs text-gray-500">买家:</span>
                            <div className="text-sm text-gray-900">
                              {payment.order.buyer.name || '未设置'}
                            </div>
                            <div className="text-xs text-gray-400">
                              {payment.order.buyer.email}
                            </div>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">卖家:</span>
                            <div className="text-sm text-gray-900">
                              {payment.order.seller.name || '未设置'}
                            </div>
                            <div className="text-xs text-gray-400">
                              {payment.order.seller.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(payment.status)}`}>
                          {getStatusText(payment.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>创建: {formatDate(payment.createdAt.toString())}</div>
                        {payment.confirmedAt && (
                          <div>确认: {formatDate(payment.confirmedAt.toString())}</div>
                        )}
                        {payment.refundedAt && (
                          <div>退款: {formatDate(payment.refundedAt.toString())}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-col space-y-1">
                          {payment.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => handlePaymentAction(payment.id, 'confirmPayment', { notes: '管理员确认支付' })}
                                disabled={actionLoading === payment.id}
                                className="text-green-600 hover:text-green-900 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {actionLoading === payment.id ? '🔄 确认中...' : '✅ 确认支付'}
                              </button>
                              <button
                                onClick={() => handlePaymentAction(payment.id, 'rejectPayment', { notes: '管理员拒绝支付' })}
                                disabled={actionLoading === payment.id}
                                className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {actionLoading === payment.id ? '🔄 拒绝中...' : '❌ 拒绝支付'}
                              </button>
                            </>
                          )}

                          {payment.status === 'CONFIRMED' && (
                            <button
                              onClick={() => {
                                const refundAmount = prompt('请输入退款金额 (USDT):', payment.amount.toString())
                                if (refundAmount && parseFloat(refundAmount) > 0) {
                                  handlePaymentAction(payment.id, 'processRefund', {
                                    refundAmount: parseFloat(refundAmount),
                                    notes: '管理员处理退款'
                                  })
                                }
                              }}
                              disabled={actionLoading === payment.id}
                              className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                            >
                              {actionLoading === payment.id ? '处理中...' : '处理退款'}
                            </button>
                          )}

                          <button
                            onClick={() => {
                              const notes = prompt('请输入备注:', payment.adminNotes || '')
                              if (notes !== null) {
                                handlePaymentAction(payment.id, 'addNotes', { notes })
                              }
                            }}
                            disabled={actionLoading === payment.id}
                            className="text-gray-600 hover:text-gray-900 disabled:opacity-50"
                          >
                            {actionLoading === payment.id ? '处理中...' : '添加备注'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
