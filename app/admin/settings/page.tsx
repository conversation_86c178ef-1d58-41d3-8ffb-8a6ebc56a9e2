'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface SystemSettings {
  platform: {
    name: string
    description: string
    contactEmail: string
    supportPhone: string
  }
  fees: {
    platformFeeRate: number
    guarantorFeeRate: number
    withdrawalFeeRate: number
    minimumOrderAmount: number
    maximumOrderAmount: number
  }
  security: {
    requireEmailVerification: boolean
    requirePhoneVerification: boolean
    enableTwoFactorAuth: boolean
    sessionTimeoutMinutes: number
    maxLoginAttempts: number
  }
  trading: {
    allowGuestBrowsing: boolean
    requireKycForSelling: boolean
    autoApproveProducts: boolean
    disputeTimeoutDays: number
    reviewTimeoutDays: number
  }
  notifications: {
    emailNotifications: boolean
    smsNotifications: boolean
    pushNotifications: boolean
    adminAlerts: boolean
  }
}

export default function AdminSettings() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('platform')
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const response = await fetch('/api/admin/settings')
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
      } else {
        setError('获取系统设置失败')
      }
    } catch (error) {
      console.error('系统设置页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveSettings = async (category: string, categorySettings: any) => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          category,
          settings: categorySettings
        })
      })

      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
        alert('设置已保存')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '保存失败')
      }
    } catch (error) {
      console.error('保存设置错误:', error)
      alert('网络错误')
    } finally {
      setIsSaving(false)
    }
  }

  const handleSystemAction = async (action: string) => {
    if (!confirm(`确定要执行 ${action} 操作吗？`)) {
      return
    }

    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })

      if (response.ok) {
        const data = await response.json()
        if (action === 'resetToDefaults') {
          setSettings(data.settings)
        }
        alert(data.message || '操作完成')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      console.error('系统操作错误:', error)
      alert('网络错误')
    }
  }

  const updateSettings = (category: keyof SystemSettings, field: string, value: any) => {
    if (!settings) return
    
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [field]: value
      }
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  if (!settings) {
    return <div>设置加载失败</div>
  }

  const tabs = [
    { id: 'platform', name: '平台设置' },
    { id: 'fees', name: '费率设置' },
    { id: 'security', name: '安全设置' },
    { id: 'trading', name: '交易设置' },
    { id: 'notifications', name: '通知设置' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 系统设置
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">系统设置</h1>
            
            {/* 系统操作按钮 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-wrap gap-4">
                <button
                  onClick={() => handleSystemAction('resetToDefaults')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  重置为默认设置
                </button>
                <button
                  onClick={() => handleSystemAction('exportSettings')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  导出设置
                </button>
                <button
                  onClick={() => handleSystemAction('clearCache')}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  清理缓存
                </button>
              </div>
            </div>
          </div>

          {/* 标签页 */}
          <div className="bg-white shadow rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {/* 平台设置 */}
              {activeTab === 'platform' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">平台基础信息</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">平台名称</label>
                      <input
                        type="text"
                        value={settings.platform.name}
                        onChange={(e) => updateSettings('platform', 'name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">联系邮箱</label>
                      <input
                        type="email"
                        value={settings.platform.contactEmail}
                        onChange={(e) => updateSettings('platform', 'contactEmail', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">客服电话</label>
                      <input
                        type="text"
                        value={settings.platform.supportPhone}
                        onChange={(e) => updateSettings('platform', 'supportPhone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">平台描述</label>
                      <textarea
                        value={settings.platform.description}
                        onChange={(e) => updateSettings('platform', 'description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => handleSaveSettings('platform', settings.platform)}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存平台设置'}
                  </button>
                </div>
              )}

              {/* 费率设置 */}
              {activeTab === 'fees' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">费率配置</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">平台手续费率 (%)</label>
                      <input
                        type="number"
                        step="0.001"
                        min="0"
                        max="1"
                        value={settings.fees.platformFeeRate * 100}
                        onChange={(e) => updateSettings('fees', 'platformFeeRate', parseFloat(e.target.value) / 100)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">担保人费率 (%)</label>
                      <input
                        type="number"
                        step="0.001"
                        min="0"
                        max="1"
                        value={settings.fees.guarantorFeeRate * 100}
                        onChange={(e) => updateSettings('fees', 'guarantorFeeRate', parseFloat(e.target.value) / 100)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">提现手续费率 (%)</label>
                      <input
                        type="number"
                        step="0.001"
                        min="0"
                        max="1"
                        value={settings.fees.withdrawalFeeRate * 100}
                        onChange={(e) => updateSettings('fees', 'withdrawalFeeRate', parseFloat(e.target.value) / 100)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">最小订单金额 (USDT)</label>
                      <input
                        type="number"
                        min="1"
                        value={settings.fees.minimumOrderAmount}
                        onChange={(e) => updateSettings('fees', 'minimumOrderAmount', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">最大订单金额 (USDT)</label>
                      <input
                        type="number"
                        min="1"
                        value={settings.fees.maximumOrderAmount}
                        onChange={(e) => updateSettings('fees', 'maximumOrderAmount', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => handleSaveSettings('fees', settings.fees)}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存费率设置'}
                  </button>
                </div>
              )}

              {/* 安全设置 */}
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">安全配置</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">要求邮箱验证</label>
                        <p className="text-sm text-gray-500">新用户注册时必须验证邮箱</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.security.requireEmailVerification}
                        onChange={(e) => updateSettings('security', 'requireEmailVerification', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">要求手机验证</label>
                        <p className="text-sm text-gray-500">新用户注册时必须验证手机号</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.security.requirePhoneVerification}
                        onChange={(e) => updateSettings('security', 'requirePhoneVerification', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">启用双因子认证</label>
                        <p className="text-sm text-gray-500">强制用户使用2FA</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.security.enableTwoFactorAuth}
                        onChange={(e) => updateSettings('security', 'enableTwoFactorAuth', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">会话超时时间 (分钟)</label>
                        <input
                          type="number"
                          min="5"
                          max="1440"
                          value={settings.security.sessionTimeoutMinutes}
                          onChange={(e) => updateSettings('security', 'sessionTimeoutMinutes', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">最大登录尝试次数</label>
                        <input
                          type="number"
                          min="3"
                          max="10"
                          value={settings.security.maxLoginAttempts}
                          onChange={(e) => updateSettings('security', 'maxLoginAttempts', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleSaveSettings('security', settings.security)}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存安全设置'}
                  </button>
                </div>
              )}

              {/* 交易设置 */}
              {activeTab === 'trading' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">交易配置</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">允许游客浏览</label>
                        <p className="text-sm text-gray-500">未登录用户可以浏览商品</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.trading.allowGuestBrowsing}
                        onChange={(e) => updateSettings('trading', 'allowGuestBrowsing', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">卖家需要KYC认证</label>
                        <p className="text-sm text-gray-500">发布商品前必须完成身份认证</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.trading.requireKycForSelling}
                        onChange={(e) => updateSettings('trading', 'requireKycForSelling', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">自动审核商品</label>
                        <p className="text-sm text-gray-500">新发布的商品自动通过审核</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.trading.autoApproveProducts}
                        onChange={(e) => updateSettings('trading', 'autoApproveProducts', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">纠纷超时天数</label>
                        <input
                          type="number"
                          min="1"
                          max="30"
                          value={settings.trading.disputeTimeoutDays}
                          onChange={(e) => updateSettings('trading', 'disputeTimeoutDays', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">评价超时天数</label>
                        <input
                          type="number"
                          min="1"
                          max="30"
                          value={settings.trading.reviewTimeoutDays}
                          onChange={(e) => updateSettings('trading', 'reviewTimeoutDays', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleSaveSettings('trading', settings.trading)}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存交易设置'}
                  </button>
                </div>
              )}

              {/* 通知设置 */}
              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">通知配置</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">邮件通知</label>
                        <p className="text-sm text-gray-500">向用户发送邮件通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.emailNotifications}
                        onChange={(e) => updateSettings('notifications', 'emailNotifications', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">短信通知</label>
                        <p className="text-sm text-gray-500">向用户发送短信通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.smsNotifications}
                        onChange={(e) => updateSettings('notifications', 'smsNotifications', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">推送通知</label>
                        <p className="text-sm text-gray-500">向用户发送浏览器推送通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.pushNotifications}
                        onChange={(e) => updateSettings('notifications', 'pushNotifications', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">管理员警报</label>
                        <p className="text-sm text-gray-500">向管理员发送重要事件警报</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.adminAlerts}
                        onChange={(e) => updateSettings('notifications', 'adminAlerts', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => handleSaveSettings('notifications', settings.notifications)}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存通知设置'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
