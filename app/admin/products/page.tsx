'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Product {
  id: string
  title: string
  description: string
  priceUsdt: number
  priceRmb: number | null
  category: string
  status: string
  createdAt: Date
  updatedAt: Date
  seller: {
    id: string
    userId: string | null
    name: string | null
    email: string | null
    creditScore: number
  }
  _count: {
    orders: number
  }
}

interface ProductsResponse {
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminProducts() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [pagination, setPagination] = useState<ProductsResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm, statusFilter, categoryFilter])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })
      
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)
      if (categoryFilter) params.append('category', categoryFilter)

      const response = await fetch(`/api/admin/products?${params}`)
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: ProductsResponse = await response.json()
        setProducts(data.products)
        setPagination(data.pagination)
      } else {
        setError('获取商品列表失败')
      }
    } catch (error) {
      console.error('商品管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProductAction = async (productId: string, action: string, data?: any) => {
    if (!confirm(`确定要${action === 'delete' ? '删除' : '更新'}这个商品吗？`)) {
      return
    }

    setActionLoading(productId)
    try {
      const response = await fetch('/api/admin/products', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId,
          action,
          data
        })
      })

      if (response.ok) {
        // 刷新商品列表
        checkAdminAccess()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      console.error('商品操作错误:', error)
      alert('网络错误')
    } finally {
      setActionLoading(null)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    checkAdminAccess()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'ACTIVE': '上架中',
      'INACTIVE': '已下架',
      'PENDING': '待审核',
      'REJECTED': '已拒绝'
    }
    return statusMap[status] || status
  }

  const getStatusBadgeColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'REJECTED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-3">
              <Link href="/admin" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
              <span className="text-xl font-bold text-gray-900">商品管理</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和搜索 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">商品管理</h1>
            
            {/* 搜索和筛选 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="搜索商品标题、描述或卖家..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有状态</option>
                    <option value="ACTIVE">上架中</option>
                    <option value="INACTIVE">已下架</option>
                    <option value="PENDING">待审核</option>
                    <option value="REJECTED">已拒绝</option>
                  </select>
                </div>
                <div>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有分类</option>
                    <option value="数码产品">数码产品</option>
                    <option value="服装配饰">服装配饰</option>
                    <option value="家居用品">家居用品</option>
                    <option value="图书音像">图书音像</option>
                    <option value="运动户外">运动户外</option>
                    <option value="其他">其他</option>
                  </select>
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  搜索
                </button>
              </form>
            </div>
          </div>

          {/* 统计信息 */}
          {pagination && (
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  共 {pagination.total} 个商品，第 {pagination.page} / {pagination.totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 商品列表 */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      商品信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      价格
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      卖家
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      订单数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      发布时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                            {product.title}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {product.description}
                          </div>
                          <div className="text-xs text-gray-400">
                            {product.category}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{product.priceUsdt} USDT</div>
                        {product.priceRmb && (
                          <div className="text-xs text-gray-500">≈ ¥{product.priceRmb}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {product.seller.name || '未设置'}
                        </div>
                        <div className="text-sm text-gray-500">{product.seller.email}</div>
                        <div className="text-xs text-gray-400">
                          信用: {product.seller.creditScore}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(product.status)}`}>
                          {getStatusText(product.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product._count.orders}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(product.createdAt.toString())}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-col space-y-1">
                          {product.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => handleProductAction(product.id, 'updateStatus', { status: 'ACTIVE' })}
                                disabled={actionLoading === product.id}
                                className="text-green-600 hover:text-green-900 disabled:opacity-50"
                              >
                                {actionLoading === product.id ? '处理中...' : '通过审核'}
                              </button>
                              <button
                                onClick={() => handleProductAction(product.id, 'updateStatus', { status: 'REJECTED' })}
                                disabled={actionLoading === product.id}
                                className="text-red-600 hover:text-red-900 disabled:opacity-50"
                              >
                                {actionLoading === product.id ? '处理中...' : '拒绝'}
                              </button>
                            </>
                          )}
                          
                          {product.status === 'ACTIVE' && (
                            <button
                              onClick={() => handleProductAction(product.id, 'updateStatus', { status: 'INACTIVE' })}
                              disabled={actionLoading === product.id}
                              className="text-yellow-600 hover:text-yellow-900 disabled:opacity-50"
                            >
                              {actionLoading === product.id ? '处理中...' : '下架'}
                            </button>
                          )}
                          
                          {product.status === 'INACTIVE' && (
                            <button
                              onClick={() => handleProductAction(product.id, 'updateStatus', { status: 'ACTIVE' })}
                              disabled={actionLoading === product.id}
                              className="text-green-600 hover:text-green-900 disabled:opacity-50"
                            >
                              {actionLoading === product.id ? '处理中...' : '上架'}
                            </button>
                          )}
                          
                          <button
                            onClick={() => handleProductAction(product.id, 'delete')}
                            disabled={actionLoading === product.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          >
                            {actionLoading === product.id ? '处理中...' : '删除'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
