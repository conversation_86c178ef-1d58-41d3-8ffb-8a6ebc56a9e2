"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  TicketIcon,
  PlusIcon,
  EyeIcon,
  ArrowPathIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  DocumentArrowDownIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserIcon,
  GiftIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '@/components/admin/AdminLayout'

interface RedemptionCode {
  id: string
  codeValue?: string
  codeType: string
  rewardType: string
  rewardValue: number
  rewardUnit: string
  distributionType: string
  maxUses: number
  usedCount: number
  status: string
  validFrom: string
  validUntil: string
  title: string
  description?: string
  batchId?: string
  targetUser?: {
    id: string
    name: string
    email: string
  }
  createdBy: {
    id: string
    name: string
  }
  transactions: Array<{
    id: string
    userId: string
    transactionType: string
    createdAt: string
    user: {
      name: string
    }
  }>
}

export default function AdminRedemptionCodesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [redemptionCodes, setRedemptionCodes] = useState<RedemptionCode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [creating, setCreating] = useState(false)
  
  // 筛选和搜索
  const [filters, setFilters] = useState({
    codeType: '',
    rewardType: '',
    status: '',
    batchId: '',
    search: ''
  })

  // 创建表单
  const [createForm, setCreateForm] = useState({
    codeType: 'REDEMPTION_CODE',
    rewardType: 'CASH_CREDIT',
    rewardValue: 10,
    rewardUnit: 'USDT',
    distributionType: 'PUBLIC',
    targetUserIds: [] as string[],
    quantity: 1,
    validDays: 30,
    maxUses: 1,
    title: '',
    description: '',
    terms: '',
    batchName: ''
  })

  const codeTypes = [
    { value: 'DIRECT_CREDIT', label: '直购式', description: '直接添加到用户账户' },
    { value: 'REDEMPTION_CODE', label: '兑换码式', description: '生成兑换码供用户输入' }
  ]

  const rewardTypes = [
    { value: 'CASH_CREDIT', label: '现金奖励', description: '直接增加用户余额' },
    { value: 'WITHDRAWAL_FEE_DISCOUNT', label: '提现手续费减免', description: '减免提现手续费' },
    { value: 'SHOPPING_VOUCHER', label: '购物券', description: '购物时可抵扣' }
  ]

  const distributionTypes = [
    { value: 'PUBLIC', label: '公开发放', description: '任何人都可以使用' },
    { value: 'TARGETED', label: '定向发放', description: '指定用户才能使用' },
    { value: 'BATCH', label: '批量发放', description: '批量生成供分发' }
  ]

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/auth/signin')
      return
    }

    fetchRedemptionCodes()
  }, [session, status])

  const fetchRedemptionCodes = async () => {
    try {
      setLoading(true)
      setError('')

      const queryParams = new URLSearchParams()
      if (filters.codeType) queryParams.append('codeType', filters.codeType)
      if (filters.rewardType) queryParams.append('rewardType', filters.rewardType)
      if (filters.status) queryParams.append('status', filters.status)
      if (filters.batchId) queryParams.append('batchId', filters.batchId)
      if (filters.search) queryParams.append('search', filters.search)

      const response = await fetch(`/api/admin/redemption-codes?${queryParams.toString()}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setRedemptionCodes(result.data.redemptionCodes)
        } else {
          setError(result.error || '获取兑换券列表失败')
        }
      } else {
        setError('获取兑换券列表失败')
      }
    } catch (error) {
      console.error('获取兑换券列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateRedemptionCode = async () => {
    try {
      setCreating(true)

      // 验证定向发放必须指定用户
      if (createForm.distributionType === 'TARGETED') {
        const validEmails = createForm.targetUserIds.filter(email => email.trim() !== '')
        if (validEmails.length === 0) {
          alert('定向发放必须指定至少一个用户邮箱')
          return
        }
        // 简单的邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        const invalidEmails = validEmails.filter(email => !emailRegex.test(email))
        if (invalidEmails.length > 0) {
          alert(`以下邮箱格式不正确: ${invalidEmails.join(', ')}`)
          return
        }
      }

      const response = await fetch('/api/admin/redemption-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(createForm)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(`成功创建 ${result.data.quantity} 个兑换券`)
        setShowCreateModal(false)
        setCreateForm({
          codeType: 'REDEMPTION_CODE',
          rewardType: 'CASH_CREDIT',
          rewardValue: 10,
          rewardUnit: 'USDT',
          distributionType: 'PUBLIC',
          targetUserIds: [],
          quantity: 1,
          validDays: 30,
          maxUses: 1,
          title: '',
          description: '',
          terms: '',
          batchName: ''
        })
        fetchRedemptionCodes() // 刷新列表
      } else {
        alert(result.error || '创建兑换券失败')
      }
    } catch (error) {
      console.error('创建兑换券失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setCreating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'USED':
        return 'bg-blue-100 text-blue-800'
      case 'EXPIRED':
        return 'bg-red-100 text-red-800'
      case 'REVOKED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'ACTIVE': '有效',
      'USED': '已用完',
      'EXPIRED': '已过期',
      'REVOKED': '已撤销'
    }
    return statusMap[status] || status
  }

  const getCodeTypeText = (type: string) => {
    return codeTypes.find(t => t.value === type)?.label || type
  }

  const getRewardTypeText = (type: string) => {
    return rewardTypes.find(t => t.value === type)?.label || type
  }

  const getDistributionTypeText = (type: string) => {
    return distributionTypes.find(t => t.value === type)?.label || type
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">兑换券管理</h1>
            <p className="text-gray-600">管理平台兑换券的创建、发放和使用</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => {
                // 重置表单状态
                setCreateForm({
                  codeType: 'REDEMPTION_CODE',
                  rewardType: 'CASH_CREDIT',
                  rewardValue: 10,
                  rewardUnit: 'USDT',
                  distributionType: 'PUBLIC',
                  targetUserIds: [],
                  quantity: 1,
                  validDays: 30,
                  maxUses: 1,
                  title: '',
                  description: '',
                  terms: '',
                  batchName: ''
                })
                setShowCreateModal(true)
              }}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              创建兑换券
            </button>
            <button
              onClick={fetchRedemptionCodes}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              刷新
            </button>
          </div>
        </div>

        {/* 筛选器 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
              <select
                value={filters.codeType}
                onChange={(e) => setFilters(prev => ({ ...prev, codeType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部类型</option>
                {codeTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">奖励类型</label>
              <select
                value={filters.rewardType}
                onChange={(e) => setFilters(prev => ({ ...prev, rewardType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部奖励</option>
                {rewardTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部状态</option>
                <option value="ACTIVE">有效</option>
                <option value="USED">已用完</option>
                <option value="EXPIRED">已过期</option>
                <option value="REVOKED">已撤销</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">批次ID</label>
              <input
                type="text"
                value={filters.batchId}
                onChange={(e) => setFilters(prev => ({ ...prev, batchId: e.target.value }))}
                placeholder="输入批次ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">搜索</label>
              <div className="relative">
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="兑换码、标题..."
                  className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button
              onClick={fetchRedemptionCodes}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              应用筛选
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 兑换券列表 */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ) : redemptionCodes.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      兑换券信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型/奖励
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      使用情况
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      发放方式
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      有效期
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {redemptionCodes.map((code) => (
                    <tr key={code.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {code.title}
                          </div>
                          {code.codeValue && (
                            <div className="text-xs font-mono text-gray-600">
                              {code.codeValue}
                            </div>
                          )}
                          {code.description && (
                            <div className="text-xs text-gray-500 truncate max-w-32">
                              {code.description}
                            </div>
                          )}
                          {code.batchId && (
                            <div className="text-xs text-blue-600">
                              批次: {code.batchId}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {getCodeTypeText(code.codeType)}
                          </div>
                          <div className="text-sm text-gray-600">
                            {getRewardTypeText(code.rewardType)}
                          </div>
                          <div className="text-sm font-medium text-green-600">
                            {code.rewardValue} {code.rewardUnit}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {code.usedCount} / {code.maxUses}
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(code.status)}`}>
                            {getStatusText(code.status)}
                          </span>
                          {code.transactions.length > 0 && (
                            <div className="text-xs text-gray-500 mt-1">
                              最近使用: {code.transactions[0].user.name}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {getDistributionTypeText(code.distributionType)}
                          </div>
                          {code.targetUser && (
                            <div className="text-xs text-gray-500">
                              目标: {code.targetUser.name}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          <div>开始: {new Date(code.validFrom).toLocaleDateString('zh-CN')}</div>
                          <div>结束: {new Date(code.validUntil).toLocaleDateString('zh-CN')}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              // 查看详情功能
                              const details = `兑换券详情:
标题: ${code.title}
类型: ${getCodeTypeText(code.codeType)}
奖励: ${code.rewardValue} ${code.rewardUnit} (${getRewardTypeText(code.rewardType)})
状态: ${getStatusText(code.status)}
使用情况: ${code.usedCount}/${code.maxUses}
${code.codeValue ? `兑换码: ${code.codeValue}` : ''}
${code.description ? `描述: ${code.description}` : ''}`
                              alert(details)
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <TicketIcon className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2 text-sm text-gray-500">暂无兑换券记录</p>
            </div>
          )}
        </div>

        {/* 创建兑换券模态框 */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">创建兑换券</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">兑换券类型</label>
                    <select
                      value={createForm.codeType}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, codeType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {codeTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      {codeTypes.find(t => t.value === createForm.codeType)?.description}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">奖励类型</label>
                    <select
                      value={createForm.rewardType}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, rewardType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {rewardTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      {rewardTypes.find(t => t.value === createForm.rewardType)?.description}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">奖励价值</label>
                    <input
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={createForm.rewardValue}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, rewardValue: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">奖励单位</label>
                    <select
                      value={createForm.rewardUnit}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, rewardUnit: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="USDT">USDT</option>
                      <option value="PERCENT">百分比</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">发放方式</label>
                  <select
                    value={createForm.distributionType}
                    onChange={(e) => {
                      const newDistributionType = e.target.value
                      setCreateForm(prev => ({
                        ...prev,
                        distributionType: newDistributionType,
                        targetUserIds: newDistributionType === 'TARGETED' ? [''] : []
                      }))
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {distributionTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    {distributionTypes.find(t => t.value === createForm.distributionType)?.description}
                  </p>
                </div>

                {/* 定向发放时显示用户指定字段 */}
                {createForm.distributionType === 'TARGETED' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">指定用户 *</label>
                    <div className="space-y-2">
                      {createForm.targetUserIds.map((userId, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="email"
                            placeholder="输入用户邮箱"
                            value={userId}
                            onChange={(e) => {
                              const newTargetUserIds = [...createForm.targetUserIds]
                              newTargetUserIds[index] = e.target.value
                              setCreateForm(prev => ({ ...prev, targetUserIds: newTargetUserIds }))
                            }}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newTargetUserIds = createForm.targetUserIds.filter((_, i) => i !== index)
                              setCreateForm(prev => ({ ...prev, targetUserIds: newTargetUserIds }))
                            }}
                            className="px-3 py-2 text-red-600 hover:text-red-800"
                          >
                            删除
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          setCreateForm(prev => ({ ...prev, targetUserIds: [...prev.targetUserIds, ''] }))
                        }}
                        className="w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-600 hover:border-gray-400 hover:text-gray-800"
                      >
                        + 添加用户
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      只有指定的用户可以使用这些兑换券
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">数量</label>
                    <input
                      type="number"
                      min="1"
                      max="10000"
                      value={createForm.quantity}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, quantity: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">有效期 (天)</label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      value={createForm.validDays}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, validDays: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">最大使用次数</label>
                    <input
                      type="number"
                      min="1"
                      max="1000"
                      value={createForm.maxUses}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, maxUses: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标题 *</label>
                  <input
                    type="text"
                    value={createForm.title}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="兑换券标题"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={createForm.description}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    placeholder="兑换券描述"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">使用条款</label>
                  <textarea
                    value={createForm.terms}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, terms: e.target.value }))}
                    rows={2}
                    placeholder="使用条款和限制"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">批次名称</label>
                  <input
                    type="text"
                    value={createForm.batchName}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, batchName: e.target.value }))}
                    placeholder="可选，用于标识此批次"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleCreateRedemptionCode}
                  disabled={creating || !createForm.title}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {creating ? '创建中...' : '创建兑换券'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
