'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon,
  AcademicCapIcon,
  PhotoIcon
} from '@heroicons/react/24/outline'

interface HelpArticle {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  subcategory: string | null
  tags: string
  keywords: string
  articleType: string
  status: string
  isFeatured: boolean
  sortOrder: number
  difficulty: string
  viewCount: number
  helpfulCount: number
  notHelpfulCount: number
  version: number
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string | null
    email: string | null
  }
  reviewer: {
    id: string
    name: string | null
    email: string | null
  } | null
}

interface HelpArticleListResponse {
  articles: HelpArticle[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function AdminHelpPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [articles, setArticles] = useState<HelpArticle[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选和搜索状态
  const [currentPage, setCurrentPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 检查管理员权限
  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    checkAdminAccess()
  }, [session, status])

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/admin/check')
      if (!response.ok) {
        if (response.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError('权限验证失败')
        }
        setIsLoading(false)
        return
      }
      
      // 权限验证通过，加载数据
      await fetchArticles()
    } catch (error) {
      console.error('权限检查失败:', error)
      setError('网络错误')
      setIsLoading(false)
    }
  }

  // 获取帮助文章列表
  const fetchArticles = async (page = 1) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      })

      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (typeFilter !== 'all') params.append('articleType', typeFilter)
      if (searchQuery.trim()) params.append('search', searchQuery.trim())

      const response = await fetch(`/api/admin/help?${params}`)
      if (response.ok) {
        const data: HelpArticleListResponse = await response.json()
        setArticles(data.articles)
        setPagination(data.pagination)
        setCurrentPage(page)
      } else {
        setError('获取帮助文章列表失败')
      }
    } catch (error) {
      console.error('获取帮助文章列表失败:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 删除文章
  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`确定要删除文章"${title}"吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/help/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('文章删除成功')
        fetchArticles(currentPage)
      } else {
        const data = await response.json()
        alert(data.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    fetchArticles(1)
  }

  // 重置筛选
  const handleReset = () => {
    setStatusFilter('all')
    setCategoryFilter('all')
    setTypeFilter('all')
    setSearchQuery('')
    setCurrentPage(1)
    fetchArticles(1)
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      'DRAFT': { text: '草稿', color: 'bg-gray-100 text-gray-800' },
      'PUBLISHED': { text: '已发布', color: 'bg-green-100 text-green-800' },
      'ARCHIVED': { text: '已归档', color: 'bg-yellow-100 text-yellow-800' },
      'DELETED': { text: '已删除', color: 'bg-red-100 text-red-800' }
    }
    return statusMap[status as keyof typeof statusMap] || { text: status, color: 'bg-gray-100 text-gray-800' }
  }

  // 获取分类显示
  const getCategoryDisplay = (category: string) => {
    const categoryMap = {
      'payment': { text: '支付相关', icon: BookOpenIcon },
      'trading': { text: '交易流程', icon: AcademicCapIcon },
      'security': { text: '安全提示', icon: QuestionMarkCircleIcon },
      'account': { text: '账户设置', icon: BookOpenIcon },
      'general': { text: '一般', icon: BookOpenIcon }
    }
    return categoryMap[category as keyof typeof categoryMap] || { text: category, icon: BookOpenIcon }
  }

  // 获取文章类型显示
  const getTypeDisplay = (type: string) => {
    const typeMap = {
      'GUIDE': { text: '指南', color: 'text-blue-600' },
      'FAQ': { text: 'FAQ', color: 'text-green-600' },
      'TUTORIAL': { text: '教程', color: 'text-purple-600' },
      'TIPS': { text: '提示', color: 'text-orange-600' },
      'TROUBLESHOOTING': { text: '故障排除', color: 'text-red-600' }
    }
    return typeMap[type as keyof typeof typeMap] || { text: type, color: 'text-gray-600' }
  }

  // 获取难度显示
  const getDifficultyDisplay = (difficulty: string) => {
    const difficultyMap = {
      'BEGINNER': { text: '初级', color: 'bg-green-100 text-green-800' },
      'INTERMEDIATE': { text: '中级', color: 'bg-yellow-100 text-yellow-800' },
      'ADVANCED': { text: '高级', color: 'bg-red-100 text-red-800' }
    }
    return difficultyMap[difficulty as keyof typeof difficultyMap] || { text: difficulty, color: 'bg-gray-100 text-gray-800' }
  }

  if (isLoading && articles.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">访问受限</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理首页
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和操作 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">帮助中心内容管理</h1>
              <p className="mt-1 text-sm text-gray-600">
                管理帮助中心的文章、FAQ和教程内容
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/admin/help/media"
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <PhotoIcon className="h-4 w-4 mr-2" />
                媒体管理
              </Link>
              <Link
                href="/admin/help/create"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                创建文章
              </Link>
            </div>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* 状态筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部状态</option>
                <option value="DRAFT">草稿</option>
                <option value="PUBLISHED">已发布</option>
                <option value="ARCHIVED">已归档</option>
              </select>
            </div>

            {/* 分类筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分类
              </label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部分类</option>
                <option value="payment">支付相关</option>
                <option value="trading">交易流程</option>
                <option value="security">安全提示</option>
                <option value="account">账户设置</option>
                <option value="general">一般</option>
              </select>
            </div>

            {/* 类型筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                类型
              </label>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部类型</option>
                <option value="GUIDE">指南</option>
                <option value="FAQ">FAQ</option>
                <option value="TUTORIAL">教程</option>
                <option value="TIPS">提示</option>
                <option value="TROUBLESHOOTING">故障排除</option>
              </select>
            </div>

            {/* 搜索 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索标题或内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-end space-x-2">
              <button
                onClick={handleSearch}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                搜索
              </button>
              <button
                onClick={handleReset}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 帮助文章列表 */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">加载中...</p>
            </div>
          ) : articles.length === 0 ? (
            <div className="p-8 text-center">
              <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无帮助文章</h3>
              <p className="text-gray-600 mb-4">还没有创建任何帮助文章</p>
              <Link
                href="/admin/help/create"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                创建第一篇文章
              </Link>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        文章信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分类/类型
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        统计
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {articles.map((article) => {
                      const statusDisplay = getStatusDisplay(article.status)
                      const categoryDisplay = getCategoryDisplay(article.category)
                      const typeDisplay = getTypeDisplay(article.articleType)
                      const difficultyDisplay = getDifficultyDisplay(article.difficulty)
                      const CategoryIcon = categoryDisplay.icon

                      return (
                        <tr key={article.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="flex items-start">
                              <div className="flex-1">
                                <div className="flex items-center">
                                  <h3 className="text-sm font-medium text-gray-900">
                                    {article.title}
                                  </h3>
                                  {article.isFeatured && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                      精选
                                    </span>
                                  )}
                                </div>
                                {article.summary && (
                                  <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                                    {article.summary}
                                  </p>
                                )}
                                <div className="mt-1 text-xs text-gray-500">
                                  作者: {article.author.name || article.author.email} | 版本: v{article.version}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <CategoryIcon className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">{categoryDisplay.text}</span>
                            </div>
                            <div className={`text-sm font-medium ${typeDisplay.color}`}>
                              {typeDisplay.text}
                            </div>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${difficultyDisplay.color}`}>
                              {difficultyDisplay.text}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                              {statusDisplay.text}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            <div>查看: {article.viewCount}</div>
                            <div className="text-xs text-gray-500">
                              👍 {article.helpfulCount} | 👎 {article.notHelpfulCount}
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {new Date(article.createdAt).toLocaleDateString('zh-CN')}
                          </td>
                          <td className="px-6 py-4 text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link
                                href={`/admin/help/${article.id}`}
                                className="text-blue-600 hover:text-blue-900"
                                title="查看详情"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              <Link
                                href={`/admin/help/${article.id}/edit`}
                                className="text-green-600 hover:text-green-900"
                                title="编辑"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              <button
                                onClick={() => handleDelete(article.id, article.title)}
                                className="text-red-600 hover:text-red-900"
                                title="删除"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() => fetchArticles(currentPage - 1)}
                        disabled={currentPage <= 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        上一页
                      </button>
                      <button
                        onClick={() => fetchArticles(currentPage + 1)}
                        disabled={currentPage >= pagination.pages}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        下一页
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          显示第 <span className="font-medium">{(currentPage - 1) * pagination.limit + 1}</span> 到{' '}
                          <span className="font-medium">
                            {Math.min(currentPage * pagination.limit, pagination.total)}
                          </span>{' '}
                          条，共 <span className="font-medium">{pagination.total}</span> 条记录
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                          <button
                            onClick={() => fetchArticles(currentPage - 1)}
                            disabled={currentPage <= 1}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            上一页
                          </button>
                          {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                            const page = i + 1
                            return (
                              <button
                                key={page}
                                onClick={() => fetchArticles(page)}
                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                  page === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                              >
                                {page}
                              </button>
                            )
                          })}
                          <button
                            onClick={() => fetchArticles(currentPage + 1)}
                            disabled={currentPage >= pagination.pages}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            下一页
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
