'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import StatsCard from '@/components/admin/StatsCard'
import {
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface DashboardStats {
  totalUsers: number
  totalProducts: number
  totalOrders: number
  pendingOrders: number
  totalRevenue: number
  pendingPayments: number
  feedbacks?: number
  announcements?: number
  helpArticles?: number
  giftCards?: {
    total: number
    generated: number
    sold: number
    redeemed: number
    expired: number
    totalValue: number
  }
  redemptionCodes?: {
    total: number
    active: number
    used: number
    expired: number
    revoked: number
  }
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (status === 'loading') return // 等待会话加载完成
    checkAdminAccess()
  }, [session, status])

  const checkAdminAccess = async () => {
    if (status === 'loading') return // 会话还在加载中

    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      // 首先检查管理员权限
      const checkResponse = await fetch('/api/admin/check')
      const checkData = await checkResponse.json()

      console.log('管理员权限检查结果:', checkData)

      if (!checkResponse.ok) {
        if (checkResponse.status === 401) {
          router.push('/auth/signin')
          return
        } else if (checkResponse.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError(checkData.error || '权限验证失败')
        }
        setIsLoading(false)
        return
      }

      // 权限验证通过，获取仪表板数据
      const dashboardResponse = await fetch('/api/admin/dashboard')
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json()
        setStats(dashboardData)
      } else {
        setError('获取统计数据失败')
      }
    } catch (error) {
      console.error('管理员页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回首页
          </Link>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout
      title="仪表板"
      subtitle="比特市场平台管理控制台"
    >

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="总用户数"
            value={stats.totalUsers}
            icon={<UsersIcon />}
            color="blue"
            onClick={() => router.push('/admin/users')}
          />
          <StatsCard
            title="总订单数"
            value={stats.totalOrders}
            icon={<ShoppingBagIcon />}
            color="green"
            onClick={() => router.push('/admin/orders')}
          />
          <StatsCard
            title="待处理订单"
            value={stats.pendingOrders}
            icon={<ExclamationTriangleIcon />}
            color="yellow"
            onClick={() => router.push('/admin/orders')}
          />
          <StatsCard
            title="总收入"
            value={`$${(stats.totalRevenue || 0).toLocaleString()}`}
            icon={<CurrencyDollarIcon />}
            color="purple"
            onClick={() => router.push('/admin/payments')}
          />
        </div>
      )}

      {/* 快捷操作 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">用户管理</h3>
          <div className="space-y-3">
            <Link href="/admin/users" className="block text-blue-600 hover:text-blue-800">
              • 查看所有用户
            </Link>
            <Link href="/admin/mediators" className="block text-blue-600 hover:text-blue-800">
              • 中间人管理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">订单管理</h3>
          <div className="space-y-3">
            <Link href="/admin/orders" className="block text-blue-600 hover:text-blue-800">
              • 查看所有订单
            </Link>
            <Link href="/admin/escrow-orders" className="block text-blue-600 hover:text-blue-800">
              • 托管订单管理
            </Link>
            <Link href="/admin/disputes" className="block text-blue-600 hover:text-blue-800">
              • 争议处理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">财务管理</h3>
          <div className="space-y-3">
            <Link href="/admin/payments" className="block text-blue-600 hover:text-blue-800">
              • 支付管理
            </Link>
            <Link href="/admin/deposits" className="block text-blue-600 hover:text-blue-800">
              • 保证金管理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">内容管理</h3>
          <div className="space-y-3">
            <Link href="/admin/products" className="block text-blue-600 hover:text-blue-800">
              • 商品管理
            </Link>
            <Link href="/admin/announcements" className="block text-blue-600 hover:text-blue-800">
              • 公告管理
            </Link>
            <Link href="/admin/help" className="block text-blue-600 hover:text-blue-800">
              • 帮助中心
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统管理</h3>
          <div className="space-y-3">
            <Link href="/admin/settings" className="block text-blue-600 hover:text-blue-800">
              • 系统设置
            </Link>
            <Link href="/admin/reports" className="block text-blue-600 hover:text-blue-800">
              • 数据报告
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">礼品卡管理</h3>
          <div className="space-y-3">
            <Link href="/admin/giftcards" className="block text-blue-600 hover:text-blue-800">
              • 礼品卡管理
            </Link>
            <Link href="/admin/redemption-codes" className="block text-blue-600 hover:text-blue-800">
              • 兑换码管理
            </Link>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
                        <span className="text-white text-sm">⏳</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          待处理订单
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.pendingOrders}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/payments" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">💰</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          待确认付款
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.pendingPayments}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/reports" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">💎</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          平台收入 (USDT)
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.totalRevenue.toFixed(2)}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/announcements" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">📢</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          公告管理
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          平台公告
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/help" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">📚</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          帮助中心
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          内容管理
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/radar" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">💬</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          用户反馈
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.feedbacks || '0'} 条
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/giftcards" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-pink-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">🎁</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          礼品卡总数
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.giftCards?.total || '0'}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/redemption-codes" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-cyan-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">🎫</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          兑换券总数
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats.redemptionCodes?.total || '0'}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          )}

          {/* 快捷操作 */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Link
              href="/admin/orders"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">📋</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">订单管理</h3>
                    <p className="text-sm text-gray-600">查看和管理所有订单</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/users"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">👥</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">用户管理</h3>
                    <p className="text-sm text-gray-600">管理用户账户和权限</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/mediators"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-orange-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🛡️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">中间人管理</h3>
                    <p className="text-sm text-gray-600">审核中间人申请和管理</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/products"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-purple-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">📦</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">商品管理</h3>
                    <p className="text-sm text-gray-600">审核和管理商品信息</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/payments"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">💰</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">支付管理</h3>
                    <p className="text-sm text-gray-600">确认支付和放款操作</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/escrow"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-purple-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🔒</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">托管资金</h3>
                    <p className="text-sm text-gray-600">管理托管资金和释放操作</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/disputes"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-red-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">⚖️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">纠纷处理</h3>
                    <p className="text-sm text-gray-600">处理交易纠纷和申诉</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/deposits"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-emerald-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">💰</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">保证金管理</h3>
                    <p className="text-sm text-gray-600">管理用户保证金和审核申请</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/fees"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-indigo-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">⚙️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">手续费管理</h3>
                    <p className="text-sm text-gray-600">配置交易和提现手续费</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/radar"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-orange-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">💬</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">反馈管理</h3>
                    <p className="text-sm text-gray-600">处理用户反馈和建议</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/giftcards"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-pink-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🎁</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">礼品卡管理</h3>
                    <p className="text-sm text-gray-600">生成、销售和兑换礼品卡</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/giftcard-products"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-rose-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🛍️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">礼品卡商品管理</h3>
                    <p className="text-sm text-gray-600">创建和管理礼品卡商品模板</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/redemption-codes"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-cyan-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🎫</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">兑换券管理</h3>
                    <p className="text-sm text-gray-600">创建和管理平台兑换券</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/gift-redemption-orders"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-indigo-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">📋</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">礼品卡兑换券订单</h3>
                    <p className="text-sm text-gray-600">查看礼品卡和兑换券相关订单</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/cleanup"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-red-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">🗑️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">数据清理</h3>
                    <p className="text-sm text-gray-600">清理过期的礼品卡和兑换券</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/settings"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gray-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-lg">⚙️</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">系统设置</h3>
                    <p className="text-sm text-gray-600">平台配置和参数设置</p>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
