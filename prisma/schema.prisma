generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "windows", "linux-musl"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                            String                  @id @default(cuid())
  userId                        String?                 @unique
  name                          String?
  email                         String?                 @unique
  password                      String?
  emailVerified                 DateTime?
  image                         String?
  avatar                        String?
  binanceUid                    String?                 @unique
  bnbWalletAddress              String?
  city                          String?
  district                      String?
  creditScore                   Int                     @default(30)
  creditHistory                 Json?
  depositBalance                Float                   @default(0)
  frozenBalance                 Float                   @default(0)  // 冻结的担保金
  availableBalance              Float                   @default(0)  // 可用余额
  totalEarnings                 Float                   @default(0)  // 累计收入
  totalWithdrawals              Float                   @default(0)  // 累计提现
  guaranteeLevel                String                  @default("BRONZE") // 担保等级
  creditLevel                   String                  @default("BRONZE")
  creditPoints                  Int                     @default(0)
  lastCreditUpdate              DateTime                @default(now())
  minWithdrawalAmount           Float                   @default(50)
  autoWithdrawal                Boolean                 @default(false)
  lastBalanceUpdate             DateTime                @default(now())
  balanceVersion                Int                     @default(1)  // 余额版本号，用于并发控制
  withdrawalThreshold           Float                   @default(1000)
  status                        String                  @default("ACTIVE")
  bannedAt                      DateTime?
  bannedUntil                   DateTime?
  banReason                     String?
  bannedBy                      String?
  isGuarantor                   Boolean                 @default(false)
  role                          String                  @default("USER")
  riskFlags                     Json?
  riskLevel                     String                  @default("NORMAL")
  flaggedAt                     DateTime?
  flaggedBy                     String?
  flagNotes                     String?
  resetToken                    String?
  resetTokenExpiry              DateTime?
  createdAt                     DateTime                @default(now())
  updatedAt                     DateTime                @updatedAt
  isMediator                    Boolean                 @default(false)
  mediatorDeposit               Float                   @default(0)
  mediatorFeeRate               Float?
  mediatorReputation            Float                   @default(0)
  mediatorStatus                String                  @default("INACTIVE")
  mediatorVerifiedAt            DateTime?
  walletVerified                Boolean                 @default(false)
  walletVerifiedAt              DateTime?
  bnbWalletVerified             Boolean                 @default(false)
  bnbWalletVerifiedAt           DateTime?
  mediatorExperience            String?
  mediatorIntroduction          String?
  mediatorLastActiveAt          DateTime?
  mediatorSuccessRate           Float                   @default(0)
  mediatorTotalOrders           Int                     @default(0)
  guaranteedAmount              Float                   @default(0) // 当前担保总金额
  addresses                     Address[]
  adminNotes                    AdminNote[]             @relation("AdminNotes")
  authoredAnnouncements         Announcement[]          @relation("AnnouncementAuthor")
  reviewedAnnouncements         Announcement[]          @relation("AnnouncementReviewer")
  reportedCases                 ArbitrationCase[]       @relation("ReporterCases")
  arbitrationVotes              ArbitrationVote[]
  creditHistories               CreditHistory[]         @relation("UserCreditHistories")
  demands                       Demand[]
  demandOffers                  DemandOffer[]           @relation("SellerOffers")
  operatorDepositOperations     DepositOperation[]      @relation("OperatorDepositOperations")
  depositOperations             DepositOperation[]      @relation("UserDepositOperations")
  depositRecords                DepositRecord[]         @relation("UserDepositRecords")
  assignedDisputes              DisputeReport[]         @relation("DisputeAssignee")
  disputeReports                DisputeReport[]         @relation("DisputeReporter")

  favorites                     Favorite[]              @relation("UserFavorites")
  receivedFunds                 FundFreeze[]            @relation("FundFreezeToUser")
  fundFreezes                   FundFreeze[]            @relation("UserFundFreezes")
  fundTransactions              FundTransaction[]       @relation("UserFundTransactions")
  assignedGiftCardsAsAdmin      GiftCard[]              @relation("GiftCardAssigner")
  assignedGiftCards             GiftCard[]              @relation("GiftCardAssignee")
  createdGiftCards              GiftCard[]              @relation("GiftCardCreator")
  redeemedGiftCards             GiftCard[]              @relation("GiftCardRedeemer")
  boughtGiftCards               GiftCard[]              @relation("GiftCardBuyer")
  giftCardOrders                GiftCardOrder[]         @relation("GiftCardOrderUser")
  createdGiftCardProducts       GiftCardProduct[]       @relation("GiftCardProductCreator")
  updatedGiftCardProducts       GiftCardProduct[]       @relation("GiftCardProductUpdater")
  giftCardTransactions          GiftCardTransaction[]   @relation("GiftCardTransactionUser")
  reviewedGuarantorApplications GuarantorApplication[]  @relation("GuarantorApplicationReviewer")
  guarantorApplications         GuarantorApplication[]
  authoredHelpArticles          HelpArticle[]           @relation("HelpArticleAuthor")
  reviewedHelpArticles          HelpArticle[]           @relation("HelpArticleReviewer")
  uploadedHelpMedia             HelpMediaFile[]         @relation("HelpMediaUploader")
  committeeAppointer            MediatorCommittee[]     @relation("CommitteeAppointer")
  mediatorCommittee             MediatorCommittee[]

  mediatorVerifications         MediatorVerification[]  @relation("UserMediatorVerifications")
  verifierRelations             MediatorVerification[]  @relation("VerifierRelation")
  receivedMessages              Message[]               @relation("ReceivedMessages")
  sentMessages                  Message[]               @relation("SentMessages")
  ordersAsBuyer                 Order[]                 @relation("BuyerOrders")
  mediatedOrders                Order[]                 @relation("MediatorOrders")
  ordersAsSeller                Order[]                 @relation("SellerOrders")
  orderLogs                     OrderLog[]              @relation("OrderLogs")
  products                      Product[]
  createdRedemptionCodes        RedemptionCode[]        @relation("RedemptionCodeCreator")
  targetedRedemptionCodes       RedemptionCode[]        @relation("RedemptionCodeTarget")
  redemptionTransactions        RedemptionTransaction[] @relation("RedemptionTransactionUser")
  receivedReviews               Review[]                @relation("UserReviews")
  reviews                       Review[]
  createdCoupons                RewardCoupon[]          @relation("CreatedCoupons")
  rewardCoupons                 RewardCoupon[]
  securityLogs                  SecurityLog[]           @relation("UserSecurityLogs")
  settlementItems               SettlementItem[]
  configUpdates                 SystemConfig[]          @relation("ConfigUpdater")
  assignedFeedbacks             UserFeedback[]          @relation("AssignedFeedbacks")
  userFeedbacks                 UserFeedback[]          @relation("UserFeedbacks")
  userSessions                  UserSession[]           @relation("UserSessions")
  withdrawals                   Withdrawal[]
  createdSystemSettings         SystemSetting[]         @relation("SystemSettingCreator")
  updatedSystemSettings         SystemSetting[]         @relation("SystemSettingUpdater")
  notifications                 Notification[]          @relation("UserNotifications")

  // 中间人托管相关关系
  mediatorEscrows               EscrowOrder[]           @relation("MediatorEscrows")
  buyerEscrows                  EscrowOrder[]           @relation("BuyerEscrows")
  sellerEscrows                 EscrowOrder[]           @relation("SellerEscrows")
  escrowDisputesReported        EscrowDispute[]         @relation("EscrowDisputeReporter")
  escrowDisputesReported2       EscrowDispute[]         @relation("EscrowDisputeReported")
  escrowDisputesAssigned        EscrowDispute[]         @relation("EscrowDisputeAdmin")
  mediatorApplication           MediatorApplication?    @relation("MediatorApplication")
  reviewedMediatorApplications  MediatorApplication[]   @relation("MediatorApplicationReviewer")
  escrowChatMessages            EscrowChatMessage[]     @relation("EscrowChatSender")
  withdrawalVouchersUsed        WithdrawalVoucher[]     @relation("UserWithdrawalVouchers")
  withdrawalVouchersIssued      WithdrawalVoucher[]     @relation("IssuedWithdrawalVouchers")
  mediatorRewardsEarned         MediatorReward[]        @relation("MediatorRewards")

  // 新增关联关系
  blacklistedWalletsAdded       BlacklistedWallet[]     @relation("BlacklistedWalletAdder")
  riskAssessmentLogs            RiskAssessmentLog[]     @relation("UserRiskAssessments")

  // 担保金相关关联
  guaranteeTransactions         GuaranteeTransaction[]  @relation("UserGuaranteeTransactions")
  operatedGuaranteeTransactions GuaranteeTransaction[]  @relation("OperatorGuaranteeTransactions")
  operatedBatchSettlements      BatchSettlement[]       @relation("OperatorBatchSettlements")
}

model Product {
  id                String           @id @default(cuid())
  title             String
  description       String?
  images            String?
  price             Float
  city              String?
  district          String?
  latitude          Float?
  longitude         Float?
  address           String?
  locationRadius    Float?
  isLocationPublic  Boolean          @default(false)
  preferLocalTrade  Boolean          @default(false)
  stock             Int              @default(1)
  status            String           @default("AVAILABLE")
  category          String           @default("GENERAL")
  condition         String           @default("NEW")
  shippingFrom      String?
  reviewStatus      String           @default("PENDING")
  isDemandGenerated Boolean          @default(false)
  hasVariants       Boolean          @default(false)
  sellerId          String
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  favorites         Favorite[]       @relation("ProductFavorites")
  orders            Order[]
  orderItems        OrderItem[]
  seller            User             @relation(fields: [sellerId], references: [id])
  variants          ProductVariant[]
  reviews           Review[]

  @@index([status, reviewStatus, city, createdAt])
  @@index([sellerId, status, createdAt])
  @@index([category, status, createdAt])
  @@index([title])
  @@index([city, district])
  @@index([latitude, longitude])
  @@index([preferLocalTrade, city])
}

model ProductVariant {
  id         String             @id @default(cuid())
  productId  String
  sku        String?
  price      Float
  stock      Int                @default(0)
  status     String             @default("AVAILABLE")
  isDefault  Boolean            @default(false)
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt
  orderItems OrderItem[]
  product    Product            @relation(fields: [productId], references: [id], onDelete: Cascade)
  attributes VariantAttribute[]

  @@unique([productId, sku])
}

model VariantAttribute {
  id        String         @id @default(cuid())
  variantId String
  name      String
  value     String
  createdAt DateTime       @default(now())
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([variantId, name])
}

model OrderItem {
  id         String          @id @default(cuid())
  orderId    String
  productId  String
  variantId  String?
  quantity   Int             @default(1)
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime        @default(now())
  order      Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product    Product         @relation(fields: [productId], references: [id])
  variant    ProductVariant? @relation(fields: [variantId], references: [id])

  @@index([orderId], map: "OrderItem_orderId_fkey")
  @@index([productId], map: "OrderItem_productId_fkey")
  @@index([variantId], map: "OrderItem_variantId_fkey")
}

model Order {
  id                     String                   @id @default(cuid())
  orderNumber            String                   @unique @default(cuid())
  status                 String
  totalAmount            Float
  productPrice           Float
  shippingFee            Float                    @default(0)
  platformFee            Float                    @default(0)
  paymentMethod          String?
  paymentScreenshot      String?
  paymentTxHash          String?
  paymentConfirmed       Boolean                  @default(false)
  paymentPin             String?
  paymentPinExpiry       DateTime?
  paymentPinUsed         Boolean                  @default(false)
  verificationAttempts   Int                      @default(0)
  lastVerificationAt     DateTime?
  escrowStatus           String                   @default("PENDING")
  escrowAmount           Float?
  escrowFundedAt         DateTime?
  escrowReleasedAt       DateTime?
  shippingAddress        Json?
  trackingNumber         String?
  shippingCompany        String?
  receivedAt             DateTime?
  autoConfirmAt          DateTime?
  refundReason           String?
  refundAmount           Float?
  productId              String
  buyerId                String
  sellerId               String
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  mediatorId             String?
  disputeReason          String?
  disputeReportedAt      DateTime?
  disputeResolvedAt      DateTime?
  disputeStatus          String                   @default("NONE")
  escrowFee              Float                    @default(0)
  escrowFeeRate          Float                    @default(0)
  escrowNotes            String?
  useEscrow              Boolean                  @default(false)
  metadata               Json?
  adminNotes             AdminNote[]
  arbitrationCases       ArbitrationCase[]
  demandOffer            DemandOffer?
  disputeReports         DisputeReport[]
  giftCardSales          GiftCard[]               @relation("GiftCardSaleOrder")
  giftCardTransactions   GiftCardTransaction[]    @relation("GiftCardTransactionOrder")
  messages               Message[]
  buyer                  User                     @relation("BuyerOrders", fields: [buyerId], references: [id])
  mediator               User?                    @relation("MediatorOrders", fields: [mediatorId], references: [id])
  product                Product                  @relation(fields: [productId], references: [id])
  seller                 User                     @relation("SellerOrders", fields: [sellerId], references: [id])
  orderItems             OrderItem[]
  orderLogs              OrderLog[]
  pinVerifications       PaymentPinVerification[] @relation("PinVerifications")
  redemptionTransactions RedemptionTransaction[]  @relation("RedemptionTransactionOrder")
  escrowOrder            EscrowOrder?             @relation("OrderEscrow")
  blockchainTransactions BlockchainTransaction[]  @relation("OrderBlockchainTx")
  escrowPayments         EscrowPayment[]
  escrowTransactions     EscrowTransaction[]

  @@index([buyerId, status, createdAt])
  @@index([sellerId, status, createdAt])
  @@index([status, createdAt])
  @@index([paymentMethod, paymentConfirmed])
  @@index([mediatorId], map: "Order_mediatorId_fkey")
  @@index([productId], map: "Order_productId_fkey")
}

model AdminNote {
  id        String   @id @default(cuid())
  orderId   String
  adminId   String
  content   String
  isPrivate Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  admin     User     @relation("AdminNotes", fields: [adminId], references: [id])
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([adminId], map: "AdminNote_adminId_fkey")
  @@index([orderId], map: "AdminNote_orderId_fkey")
}

model OrderLog {
  id          String   @id @default(cuid())
  orderId     String
  operatorId  String?
  action      String
  description String
  oldValue    String?
  newValue    String?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  operator    User?    @relation("OrderLogs", fields: [operatorId], references: [id])
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([operatorId], map: "OrderLog_operatorId_fkey")
  @@index([orderId], map: "OrderLog_orderId_fkey")
}

model Address {
  id        String   @id @default(cuid())
  name      String
  phone     String
  province  String
  city      String
  district  String
  detail    String
  isDefault Boolean  @default(false)
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "Address_userId_fkey")
}

model Review {
  id             String   @id @default(cuid())
  rating         Int
  content        String?
  images         Json?
  type           String
  orderId        String
  productId      String
  reviewerId     String
  revieweeId     String
  canReviewUntil DateTime
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  product        Product  @relation(fields: [productId], references: [id])
  reviewee       User     @relation("UserReviews", fields: [revieweeId], references: [id])
  reviewer       User     @relation(fields: [reviewerId], references: [id])

  @@unique([orderId, reviewerId])
  @@index([revieweeId, createdAt])
  @@index([productId, rating])
  @@index([reviewerId], map: "Review_reviewerId_fkey")
}

model Message {
  id           String   @id @default(cuid())
  content      String
  messageType  String   @default("TEXT")
  status       String   @default("SENT")
  fileUrl      String?
  fileName     String?
  fileSize     Int?
  fileMimeType String?
  fileMetadata Json?
  orderId      String
  senderId     String
  receiverId   String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  order        Order    @relation(fields: [orderId], references: [id])
  receiver     User     @relation("ReceivedMessages", fields: [receiverId], references: [id])
  sender       User     @relation("SentMessages", fields: [senderId], references: [id])

  @@index([orderId, createdAt])
  @@index([receiverId], map: "Message_receiverId_fkey")
  @@index([senderId], map: "Message_senderId_fkey")
}

model Demand {
  id               String        @id @default(cuid())
  title            String
  description      String
  demandType       String
  subcategory      String?
  budget           Float
  deliveryMethod   String
  expirationTime   DateTime
  status           String        @default("OPEN")
  latitude         Float?
  longitude        Float?
  address          String?
  city             String?
  district         String?
  locationRadius   Float?
  isLocationPublic Boolean       @default(false)
  userId           String
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  user             User          @relation(fields: [userId], references: [id])
  offers           DemandOffer[]

  @@index([status, createdAt])
  @@index([demandType, status])
  @@index([city, district])
  @@index([latitude, longitude])
  @@index([userId], map: "Demand_userId_fkey")
}

model PaymentPinVerification {
  id                   String   @id @default(cuid())
  orderId              String
  submittedPin         String
  submittedOrderNumber String
  isValid              Boolean
  verificationStatus   String
  failureReason        String?
  ipAddress            String?
  userAgent            String?
  verifiedAt           DateTime @default(now())
  order                Order    @relation("PinVerifications", fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId, verifiedAt])
  @@index([verificationStatus, verifiedAt])
}

model DemandOffer {
  id         String   @id @default(cuid())
  offerPrice Float
  offerNote  String
  isAccepted Boolean  @default(false)
  demandId   String
  sellerId   String
  orderId    String?  @unique
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  demand     Demand   @relation(fields: [demandId], references: [id])
  order      Order?   @relation(fields: [orderId], references: [id])
  seller     User     @relation("SellerOffers", fields: [sellerId], references: [id])

  @@unique([demandId, sellerId])
  @@index([demandId, createdAt])
  @@index([sellerId], map: "DemandOffer_sellerId_fkey")
}

model FundFreeze {
  id              String           @id @default(cuid())
  userId          String
  amount          Float
  currency        String           @default("USDT")
  purpose         String
  relatedId       String?
  relatedType     String?
  status          String           @default("FROZEN")
  toUserId        String?
  platformFee     Float            @default(0)
  actualAmount    Float?
  confirmedBy     String?
  settledBy       String?
  cancelledBy     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  confirmedAt     DateTime?
  settledAt       DateTime?
  cancelledAt     DateTime?
  metadata        Json?
  notes           String?
  toUser          User?            @relation("FundFreezeToUser", fields: [toUserId], references: [id])
  user            User             @relation("UserFundFreezes", fields: [userId], references: [id], onDelete: Cascade)
  settlementItems SettlementItem[] @relation("SettlementFreeze")

  @@index([userId])
  @@index([status])
  @@index([purpose])
  @@index([relatedId])
  @@index([createdAt])
  @@index([toUserId], map: "FundFreeze_toUserId_fkey")
}

model Withdrawal {
  id            String    @id @default(cuid())
  userId        String
  amount        Float
  walletAddress String
  currency      String    @default("USDT")
  withdrawalFee Float     @default(0)
  actualAmount  Float
  status        String    @default("PENDING")
  reviewedBy    String?
  reviewedAt    DateTime?
  reviewNotes   String?
  processedBy   String?
  processedAt   DateTime?
  txHash        String?
  cancelledBy   String?
  cancelledAt   DateTime?
  cancelReason  String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  metadata      Json?
  notes         String?
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model BatchSettlement {
  id               String           @id @default(cuid())
  batchNumber      String           @unique
  batchId          String?          @unique // 新增：担保金系统批次ID
  settlementType   String
  type             String?          // 新增：WITHDRAWAL, EARNING_DISTRIBUTION, FEE_COLLECTION
  totalAmount      Float
  totalFee         Float
  userCount        Int
  transactionCount Int
  status           String           @default("PENDING")
  startTime        DateTime
  startedAt        DateTime?        // 新增：开始时间
  endTime          DateTime?
  completedAt      DateTime?        // 新增：完成时间
  failedAt         DateTime?        // 新增：失败时间
  createdAt        DateTime         @default(now())
  processedAt      DateTime?
  processedBy      String?
  operatorId       String?          // 新增：操作员ID
  errorMessage     String?
  metadata         Json?
  notes            String?
  settlements      SettlementItem[]
  updatedAt        DateTime         @updatedAt // 新增：更新时间

  operator User? @relation("OperatorBatchSettlements", fields: [operatorId], references: [id])

  @@index([status])
  @@index([settlementType])
  @@index([type])
  @@index([batchId])
  @@index([createdAt])
}

model SettlementItem {
  id             String          @id @default(cuid())
  batchId        String
  userId         String
  freezeId       String
  originalAmount Float
  platformFee    Float
  settlementFee  Float
  finalAmount    Float
  status         String          @default("PENDING")
  errorMessage   String?
  createdAt      DateTime        @default(now())
  processedAt    DateTime?
  batch          BatchSettlement @relation(fields: [batchId], references: [id], onDelete: Cascade)
  freeze         FundFreeze      @relation("SettlementFreeze", fields: [freezeId], references: [id])
  user           User            @relation(fields: [userId], references: [id])

  @@index([batchId])
  @@index([userId])
  @@index([status])
  @@index([freezeId], map: "SettlementItem_freezeId_fkey")
}

model FundPoolStats {
  id                 String   @id @default(cuid())
  statsDate          DateTime @unique
  date               DateTime @unique @db.Date // 新增：日期字段
  totalDeposits      Float    @default(0)
  totalFrozen        Float    @default(0)
  totalAvailable     Float    @default(0)
  totalWithdrawn     Float    @default(0)
  totalEarnings      Float    @default(0) // 新增：总收益
  totalWithdrawals   Float    @default(0) // 新增：总提现
  newDeposits        Float    @default(0) // 新增：新增存款
  newWithdrawals     Float    @default(0) // 新增：新增提现
  activeUsers        Int      @default(0)
  newUsers           Int      @default(0)
  dailyTransactions  Int      @default(0)
  transactionCount   Int      @default(0) // 新增：交易数量
  dailyVolume        Float    @default(0)
  avgTransactionSize Float    @default(0)
  averageBalance     Float    @default(0) // 新增：平均余额
  utilizationRate    Float    @default(0) // 新增：资金利用率
  bronzeUsers        Int      @default(0)
  silverUsers        Int      @default(0)
  goldUsers          Int      @default(0)
  platinumUsers      Int      @default(0)
  diamondUsers       Int      @default(0)
  platformRevenue    Float    @default(0)
  userRewards        Float    @default(0)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@index([statsDate])
  @@index([date])
}

model EscrowPayment {
  id             String    @id @default(cuid())
  amount         Float
  currency       String    @default("USDT")
  status         String
  paymentMethod  String
  paymentData    Json?
  txHash         String?
  binanceOrderId String?
  confirmations  Int       @default(0)
  fundedAt       DateTime?
  releasedAt     DateTime?
  refundedAt     DateTime?
  platformFee    Float     @default(0)
  networkFee     Float     @default(0)
  adminNotes     String?
  processedBy    String?
  orderId        String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  order          Order     @relation(fields: [orderId], references: [id])

  @@index([orderId, status])
  @@index([status, createdAt])
}

model WalletConfig {
  id             String   @id @default(cuid())
  network        String
  address        String
  currency       String
  isActive       Boolean  @default(true)
  minAmount      Float    @default(1)
  maxAmount      Float    @default(100000)
  privateKeyHash String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([network, currency])
}

model UserFeedback {
  id            String    @id @default(cuid())
  userId        String
  category      String
  title         String
  description   String
  attachments   Json?
  status        String    @default("PENDING")
  priority      String    @default("MEDIUM")
  assignedToId  String?
  adminResponse String?
  resolvedAt    DateTime?
  contactEmail  String?
  contactPhone  String?
  userAgent     String?
  browserInfo   Json?
  pageUrl       String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  assignedTo    User?     @relation("AssignedFeedbacks", fields: [assignedToId], references: [id])
  user          User      @relation("UserFeedbacks", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, category])
  @@index([status, priority])
  @@index([createdAt])
  @@index([assignedToId], map: "UserFeedback_assignedToId_fkey")
}

model Favorite {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())
  product   Product  @relation("ProductFavorites", fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation("UserFavorites", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@index([userId, createdAt])
  @@index([productId])
}

model CreditHistory {
  id          String   @id @default(cuid())
  userId      String
  changeType  String
  changeScore Int
  reason      String
  orderId     String?
  reviewId    String?
  adminId     String?
  beforeScore Int
  afterScore  Int
  metadata    Json?
  createdAt   DateTime @default(now())
  user        User     @relation("UserCreditHistories", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@index([changeType])
  @@index([createdAt])
}

model SecurityLog {
  id          String   @id @default(cuid())
  userId      String?  // 修改为可选，支持系统级日志
  action      String
  description String?  // 修改为可选
  details     Json?    // 新增：详细信息
  status      String?  // 修改为可选
  success     Boolean  @default(true) // 新增：操作是否成功
  ipAddress   String?
  userAgent   String?  @db.Text // 修改为 Text 类型
  location    String?
  metadata    Json?
  createdAt   DateTime @default(now())
  user        User?    @relation("UserSecurityLogs", fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId, createdAt])
  @@index([action])
  @@index([ipAddress])
  @@index([createdAt])
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionId    String   @unique
  deviceName   String?
  deviceType   String?
  browser      String?
  os           String?
  ipAddress    String
  location     String?
  isActive     Boolean  @default(true)
  lastActivity DateTime @default(now())
  createdAt    DateTime @default(now())
  expiresAt    DateTime
  user         User     @relation("UserSessions", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isActive])
  @@index([sessionId])
  @@index([expiresAt])
  @@index([lastActivity])
}

model Announcement {
  id          String    @id @default(cuid())
  title       String
  content     String
  summary     String?
  category    String    @default("GENERAL")
  priority    String    @default("NORMAL")
  targetUsers String    @default("ALL")
  status      String    @default("DRAFT")
  isSticky    Boolean   @default(false)
  showOnHome  Boolean   @default(false)
  publishAt   DateTime?
  expireAt    DateTime?
  viewCount   Int       @default(0)
  authorId    String
  reviewerId  String?
  reviewedAt  DateTime?
  reviewNote  String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  author      User      @relation("AnnouncementAuthor", fields: [authorId], references: [id])
  reviewer    User?     @relation("AnnouncementReviewer", fields: [reviewerId], references: [id])

  @@index([status, publishAt])
  @@index([category, priority])
  @@index([targetUsers, status])
  @@index([isSticky, publishAt])
  @@index([authorId], map: "Announcement_authorId_fkey")
  @@index([reviewerId], map: "Announcement_reviewerId_fkey")
}

model HelpArticle {
  id              String          @id @default(cuid())
  title           String
  content         String
  summary         String?
  category        String
  subcategory     String?
  tags            String
  keywords        String
  articleType     String          @default("GUIDE")
  status          String          @default("DRAFT")
  isFeatured      Boolean         @default(false)
  sortOrder       Int             @default(0)
  difficulty      String          @default("BEGINNER")
  viewCount       Int             @default(0)
  helpfulCount    Int             @default(0)
  notHelpfulCount Int             @default(0)
  authorId        String
  reviewerId      String?
  reviewedAt      DateTime?
  reviewNote      String?
  version         Int             @default(1)
  parentId        String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  author          User            @relation("HelpArticleAuthor", fields: [authorId], references: [id])
  parent          HelpArticle?    @relation("ArticleVersions", fields: [parentId], references: [id])
  versions        HelpArticle[]   @relation("ArticleVersions")
  reviewer        User?           @relation("HelpArticleReviewer", fields: [reviewerId], references: [id])
  mediaFiles      HelpMediaFile[]

  @@index([category, status])
  @@index([articleType, status])
  @@index([isFeatured, sortOrder])
  @@index([keywords])
  @@index([authorId], map: "HelpArticle_authorId_fkey")
  @@index([parentId], map: "HelpArticle_parentId_fkey")
  @@index([reviewerId], map: "HelpArticle_reviewerId_fkey")
}

model HelpMediaFile {
  id         String       @id @default(cuid())
  filename   String
  storedName String
  filePath   String
  fileUrl    String
  fileSize   Int
  mimeType   String
  mediaType  String
  metadata   Json?
  articleId  String?
  uploaderId String?
  status     String       @default("ACTIVE")
  isUsed     Boolean      @default(false)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  article    HelpArticle? @relation(fields: [articleId], references: [id])
  uploader   User?        @relation("HelpMediaUploader", fields: [uploaderId], references: [id])

  @@index([articleId, status])
  @@index([uploaderId, createdAt])
  @@index([status, isUsed])
  @@index([mediaType, status])
}

model GuarantorApplication {
  id                    String    @id @default(cuid())
  userId                String
  reason                String    @db.Text
  experience            String?   @db.Text
  creditScoreAtApply    Int
  depositBalanceAtApply Float
  status                String    @default("PENDING")
  reviewedBy            String?
  reviewNotes           String?   @db.Text
  reviewedAt            DateTime?
  metadata              Json?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  reviewer              User?     @relation("GuarantorApplicationReviewer", fields: [reviewedBy], references: [id])
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
  @@index([status, createdAt])
  @@index([reviewedBy, reviewedAt])
}

model DepositRecord {
  id              String   @id @default(cuid())
  userId          String
  amount          Float
  originalAmount  Float
  method          String
  txHash          String?
  notes           String?  @db.Text
  status          String   @default("PENDING")
  metadata        Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  paymentOrderId  String?
  pinCode         String?
  transactionHash String?
  user            User     @relation("UserDepositRecords", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
  @@index([method, status])
  @@index([createdAt])
}

model FundTransaction {
  id          String   @id @default(cuid())
  userId      String
  type        String
  amount      Float
  description String   @db.Text
  relatedId   String?
  metadata    Json?
  createdAt   DateTime @default(now())
  user        User     @relation("UserFundTransactions", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, type])
  @@index([type, createdAt])
  @@index([relatedId])
}

model FeeConfig {
  id            String    @id @default(cuid())
  type          String
  name          String
  description   String?
  enabled       Boolean   @default(true)
  feeType       String
  feeValue      Float?
  minFee        Float?
  maxFee        Float?
  tiers         Json?
  paymentMethod String?
  userType      String?
  effectiveFrom DateTime  @default(now())
  effectiveTo   DateTime?
  createdBy     String?
  updatedBy     String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  version       Int       @default(1)
  parentId      String?

  @@index([type, enabled])
  @@index([effectiveFrom, effectiveTo])
  @@index([createdAt])
}

model DepositOperation {
  id            String   @id @default(cuid())
  userId        String
  operationType String
  amount        Float
  balanceBefore Float
  balanceAfter  Float
  reason        String
  notes         String?  @db.Text
  operatorId    String
  relatedId     String?
  relatedType   String?
  createdAt     DateTime @default(now())
  operator      User     @relation("OperatorDepositOperations", fields: [operatorId], references: [id])
  user          User     @relation("UserDepositOperations", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, operationType])
  @@index([operatorId, createdAt])
  @@index([relatedId, relatedType])
  @@index([createdAt])
}

model EscrowTransaction {
  id          String    @id @default(cuid())
  orderId     String
  type        String
  amount      Float
  fromAddress String?
  toAddress   String?
  txHash      String?
  status      String    @default("PENDING")
  description String?
  createdAt   DateTime  @default(now())
  confirmedAt DateTime?
  order       Order     @relation(fields: [orderId], references: [id])

  @@index([orderId])
  @@index([txHash])
  @@index([status])
  @@index([createdAt])
}

model ArbitrationCase {
  id              String            @id @default(cuid())
  orderId         String
  reporterId      String
  reporterType    String
  reason          String
  description     String
  evidence        Json?
  status          String            @default("PENDING")
  resolution      String?
  votingDeadline  DateTime?
  createdAt       DateTime          @default(now())
  resolvedAt      DateTime?
  order           Order             @relation(fields: [orderId], references: [id])
  reporter        User              @relation("ReporterCases", fields: [reporterId], references: [id])
  votes           ArbitrationVote[]

  @@index([orderId])
  @@index([reporterId])
  @@index([status])
  @@index([createdAt])
}

model ArbitrationVote {
  id         String          @id @default(cuid())
  caseId     String?
  disputeId  String?
  voterId    String
  decision   String
  reasoning  String?
  voteWeight Int             @default(1)
  votedAt    DateTime        @default(now())
  createdAt  DateTime        @default(now())

  case       ArbitrationCase? @relation(fields: [caseId], references: [id])
  dispute    EscrowDispute?   @relation("DisputeArbitrationVotes", fields: [disputeId], references: [id])
  voter      User             @relation(fields: [voterId], references: [id])

  @@unique([caseId, voterId])
  @@unique([disputeId, voterId])
  @@index([caseId])
  @@index([disputeId])
  @@index([voterId])
  @@index([createdAt])
}

model RewardCoupon {
  id               String           @id @default(cuid())
  userId           String?
  type             String
  value            Float
  description      String
  status           String           @default("ACTIVE")
  expiresAt        DateTime
  usedAt           DateTime?
  createdAt        DateTime         @default(now())
  batchId          String?
  createdById      String?
  discountType     String           @default("FIXED")
  distributionType String           @default("TARGETED")
  maxDiscount      Float?
  maxUses          Int              @default(1)
  minAmount        Float?
  terms            String?
  title            String?
  usedCount        Int              @default(0)

  createdBy        User?            @relation("CreatedCoupons", fields: [createdById], references: [id])
  user             User?            @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([expiresAt])
  @@index([createdAt])
  @@index([distributionType])
  @@index([type])
  @@index([createdById], map: "RewardCoupon_createdById_fkey")
}

model MediatorVerification {
  id                 String    @id @default(cuid())
  userId             String
  walletAddress      String
  verificationMethod String
  verificationData   Json?
  status             String    @default("PENDING")
  verifiedBy         String?
  verifiedAt         DateTime?
  rejectionReason    String?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  user               User      @relation("UserMediatorVerifications", fields: [userId], references: [id], onDelete: Cascade)
  verifier           User?     @relation("VerifierRelation", fields: [verifiedBy], references: [id])

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([verifiedBy], map: "MediatorVerification_verifiedBy_fkey")
}



model DisputeReport {
  id              String    @id @default(cuid())
  orderId         String
  reporterId      String
  disputeType     String
  description     String    @db.Text
  evidence        Json?
  status          String    @default("PENDING")
  priority        String    @default("MEDIUM")
  assignedToId    String?
  adminResponse   String?   @db.Text
  resolutionNotes String?   @db.Text
  reportedAt      DateTime  @default(now())
  resolvedAt      DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  assignedTo      User?     @relation("DisputeAssignee", fields: [assignedToId], references: [id])
  order           Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  reporter        User      @relation("DisputeReporter", fields: [reporterId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([reporterId])
  @@index([status])
  @@index([priority])
  @@index([assignedToId])
  @@index([reportedAt])
}

model GiftCardProduct {
  id                String          @id @default(cuid())
  name              String
  description       String?
  productType       String
  faceValue         Float
  salePrice         Float
  stock             Int             @default(0)
  isActive          Boolean         @default(true)
  features          Json?
  terms             String?
  validDays         Int             @default(365)
  supportedPayments Json?
  createdById       String
  updatedById       String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  giftCards         GiftCard[]
  orders            GiftCardOrder[]
  createdBy         User            @relation("GiftCardProductCreator", fields: [createdById], references: [id])
  updatedBy         User?           @relation("GiftCardProductUpdater", fields: [updatedById], references: [id])

  @@index([productType])
  @@index([isActive])
  @@index([createdAt])
  @@index([createdById], map: "GiftCardProduct_createdById_fkey")
  @@index([updatedById], map: "GiftCardProduct_updatedById_fkey")
}

model GiftCardOrder {
  id             String          @id @default(cuid())
  orderNumber    String          @unique
  productId      String
  userId         String
  quantity       Int
  unitPrice      Float
  totalAmount    Float
  paymentMethod  String
  paymentStatus  String          @default("PENDING")
  paymentTxHash  String?
  deliveryMethod String
  status         String          @default("PENDING")
  paidAt         DateTime?
  completedAt    DateTime?
  cancelledAt    DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  giftCards      GiftCard[]
  product        GiftCardProduct @relation(fields: [productId], references: [id])
  user           User            @relation("GiftCardOrderUser", fields: [userId], references: [id])

  @@index([orderNumber])
  @@index([userId])
  @@index([productId])
  @@index([paymentStatus])
  @@index([status])
  @@index([createdAt])
}

model GiftCard {
  id            String                @id @default(cuid())
  cardCode      String                @unique
  faceValue     Float
  status        String                @default("GENERATED")
  validUntil    DateTime
  soldAt        DateTime?
  soldToId      String?
  saleOrderId   String?
  redeemedAt    DateTime?
  redeemedById  String?
  redeemedValue Float?
  createdById   String
  batchId       String?
  notes         String?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
  assignedAt    DateTime?
  assignedById  String?
  assignedToId  String?
  orderId       String?
  productId     String?
  assignedBy    User?                 @relation("GiftCardAssigner", fields: [assignedById], references: [id])
  assignedTo    User?                 @relation("GiftCardAssignee", fields: [assignedToId], references: [id])
  createdBy     User                  @relation("GiftCardCreator", fields: [createdById], references: [id], onDelete: Cascade)
  order         GiftCardOrder?        @relation(fields: [orderId], references: [id])
  product       GiftCardProduct?      @relation(fields: [productId], references: [id])
  redeemedBy    User?                 @relation("GiftCardRedeemer", fields: [redeemedById], references: [id])
  saleOrder     Order?                @relation("GiftCardSaleOrder", fields: [saleOrderId], references: [id])
  soldTo        User?                 @relation("GiftCardBuyer", fields: [soldToId], references: [id])
  transactions  GiftCardTransaction[]

  @@index([cardCode])
  @@index([status])
  @@index([validUntil])
  @@index([soldAt])
  @@index([redeemedAt])
  @@index([batchId])
  @@index([productId])
  @@index([orderId])
  @@index([assignedById], map: "GiftCard_assignedById_fkey")
  @@index([assignedToId], map: "GiftCard_assignedToId_fkey")
  @@index([createdById], map: "GiftCard_createdById_fkey")
  @@index([redeemedById], map: "GiftCard_redeemedById_fkey")
  @@index([saleOrderId], map: "GiftCard_saleOrderId_fkey")
  @@index([soldToId], map: "GiftCard_soldToId_fkey")
}

model GiftCardTransaction {
  id              String   @id @default(cuid())
  giftCardId      String
  transactionType String
  amount          Float
  userId          String?
  orderId         String?
  description     String?
  metadata        Json?
  createdAt       DateTime @default(now())
  giftCard        GiftCard @relation(fields: [giftCardId], references: [id], onDelete: Cascade)
  order           Order?   @relation("GiftCardTransactionOrder", fields: [orderId], references: [id])
  user            User?    @relation("GiftCardTransactionUser", fields: [userId], references: [id])

  @@index([giftCardId])
  @@index([transactionType])
  @@index([userId])
  @@index([createdAt])
  @@index([orderId], map: "GiftCardTransaction_orderId_fkey")
}

model RedemptionCode {
  id               String                  @id @default(cuid())
  codeValue        String?                 @unique
  codeType         String
  rewardType       String
  rewardValue      Float
  rewardUnit       String                  @default("USDT")
  distributionType String
  targetUserId     String?
  batchId          String?
  maxUses          Int                     @default(1)
  usedCount        Int                     @default(0)
  validFrom        DateTime                @default(now())
  validUntil       DateTime
  status           String                  @default("ACTIVE")
  firstUsedAt      DateTime?
  lastUsedAt       DateTime?
  createdById      String
  title            String
  description      String?
  terms            String?
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  createdBy        User                    @relation("RedemptionCodeCreator", fields: [createdById], references: [id], onDelete: Cascade)
  targetUser       User?                   @relation("RedemptionCodeTarget", fields: [targetUserId], references: [id])
  transactions     RedemptionTransaction[]

  @@index([codeValue])
  @@index([codeType])
  @@index([rewardType])
  @@index([status])
  @@index([validFrom])
  @@index([validUntil])
  @@index([targetUserId])
  @@index([batchId])
  @@index([createdById], map: "RedemptionCode_createdById_fkey")
}

model RedemptionTransaction {
  id               String         @id @default(cuid())
  redemptionCodeId String
  userId           String
  transactionType  String
  rewardValue      Float
  rewardUnit       String
  usageContext     String?
  orderId          String?
  description      String?
  metadata         Json?
  ipAddress        String?
  userAgent        String?
  createdAt        DateTime       @default(now())
  order            Order?         @relation("RedemptionTransactionOrder", fields: [orderId], references: [id])
  redemptionCode   RedemptionCode @relation(fields: [redemptionCodeId], references: [id], onDelete: Cascade)
  user             User           @relation("RedemptionTransactionUser", fields: [userId], references: [id], onDelete: Cascade)

  @@index([redemptionCodeId])
  @@index([userId])
  @@index([transactionType])
  @@index([createdAt])
  @@index([orderId], map: "RedemptionTransaction_orderId_fkey")
}

model SystemSetting {
  id          String   @id @default(cuid())
  category    String   // platform, fees, security, trading, notifications
  key         String   // 设置键名
  value       Json     // 设置值（JSON格式支持复杂数据）
  description String?  // 设置描述
  dataType    String   @default("string") // string, number, boolean, json, array
  isPublic    Boolean  @default(false) // 是否为公开设置（前端可访问）
  isEditable  Boolean  @default(true) // 是否可编辑
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?
  updatedBy   String?
  creator     User?    @relation("SystemSettingCreator", fields: [createdBy], references: [id])
  updater     User?    @relation("SystemSettingUpdater", fields: [updatedBy], references: [id])

  @@unique([category, key])
  @@index([category])
  @@index([isPublic])
  @@index([createdAt])
}

model SearchKeyword {
  id          String   @id @default(cuid())
  keyword     String   @unique
  searchCount Int      @default(1)
  resultCount Int      @default(0)
  isHot       Boolean  @default(false)
  category    String?  // 关键词分类
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([searchCount])
  @@index([isHot])
  @@index([category])
  @@index([createdAt])
}

model Notification {
  id          String    @id @default(cuid())
  userId      String
  type        String    // SYSTEM, ORDER, PAYMENT, DISPUTE, ANNOUNCEMENT
  title       String
  message     String    @db.Text
  data        Json?     // 额外数据
  isRead      Boolean   @default(false)
  readAt      DateTime?
  relatedId   String?   // 关联对象ID
  relatedType String?   // 关联对象类型
  priority    String    @default("NORMAL") // LOW, NORMAL, HIGH, URGENT
  expiresAt   DateTime? // 过期时间
  createdAt   DateTime  @default(now())
  user        User      @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([type])
  @@index([priority])
  @@index([createdAt])
  @@index([relatedId, relatedType])
}



model MediatorCommittee {
  id              String   @id @default(cuid())
  mediatorId      String
  role            String   @default("MEMBER")
  appointedAt     DateTime @default(now())
  appointedBy     String
  status          String   @default("ACTIVE")
  votingPower     Int      @default(1)
  specializations Json?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  appointer       User     @relation("CommitteeAppointer", fields: [appointedBy], references: [id])
  mediator        User     @relation(fields: [mediatorId], references: [id], onDelete: Cascade)

  @@index([mediatorId])
  @@index([status])
  @@index([appointedBy], map: "MediatorCommittee_appointedBy_fkey")
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       Json
  description String?
  category    String   @default("GENERAL")
  isPublic    Boolean  @default(false)
  updatedBy   String?
  updatedAt   DateTime @updatedAt
  createdAt   DateTime @default(now())
  updater     User?    @relation("ConfigUpdater", fields: [updatedBy], references: [id])

  @@index([category])
  @@index([isPublic])
  @@index([updatedBy], map: "SystemConfig_updatedBy_fkey")
}

// 中间人托管相关模型
model EscrowOrder {
  id                    String   @id @default(cuid())
  orderId               String   @unique
  mediatorId            String
  buyerId               String
  sellerId              String
  amount                Float
  mediatorFee           Float
  platformFee           Float
  status                String   @default("PENDING") // PENDING, FUNDED, SHIPPED, DELIVERED, COMPLETED, DISPUTED, CANCELLED
  bnbTransactionHash    String?
  mediatorWalletAddress String
  fundedAt              DateTime?
  shippedAt             DateTime?
  deliveredAt           DateTime?
  completedAt           DateTime?
  disputedAt            DateTime?
  cancelledAt           DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  order                  Order                   @relation("OrderEscrow", fields: [orderId], references: [id], onDelete: Cascade)
  mediator               User                    @relation("MediatorEscrows", fields: [mediatorId], references: [id])
  buyer                  User                    @relation("BuyerEscrows", fields: [buyerId], references: [id])
  seller                 User                    @relation("SellerEscrows", fields: [sellerId], references: [id])
  chatRoom               EscrowChatRoom?         @relation("EscrowOrderChatRoom")
  disputes               EscrowDispute[]         @relation("EscrowOrderDisputes")
  blockchainTransactions BlockchainTransaction[] @relation("EscrowBlockchainTx")

  @@index([mediatorId])
  @@index([buyerId])
  @@index([sellerId])
  @@index([status])
  @@index([createdAt])
}

model EscrowChatRoom {
  id            String   @id @default(cuid())
  escrowOrderId String   @unique
  roomCode      String   @unique
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  escrowOrder EscrowOrder         @relation("EscrowOrderChatRoom", fields: [escrowOrderId], references: [id], onDelete: Cascade)
  messages    EscrowChatMessage[] @relation("ChatRoomMessages")

  @@index([roomCode])
  @@index([isActive])
}

model EscrowChatMessage {
  id           String   @id @default(cuid())
  content      String
  messageType  String   @default("TEXT")
  status       String   @default("SENT")
  fileUrl      String?
  fileName     String?
  fileSize     Int?
  fileMimeType String?
  fileMetadata Json?
  chatRoomId   String
  senderId     String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  chatRoom     EscrowChatRoom @relation("ChatRoomMessages", fields: [chatRoomId], references: [id], onDelete: Cascade)
  sender       User           @relation("EscrowChatSender", fields: [senderId], references: [id])

  @@index([chatRoomId, createdAt])
  @@index([senderId])
}

model EscrowDispute {
  id            String   @id @default(cuid())
  escrowOrderId String
  reporterId    String
  reportedId    String
  reason        String
  description   String   @db.Text
  evidence      Json?
  status        String   @default("PENDING") // PENDING, UNDER_REVIEW, VOTING, RESOLVED, CLOSED
  priority      String   @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  adminAssigned String?
  resolution    String?  @db.Text
  resolvedAt    DateTime?
  votingStartsAt  DateTime?
  votingEndsAt    DateTime?
  finalDecision   String? // e.g., FAVOR_BUYER, FAVOR_SELLER, SPLIT_FUNDS
  decisionDetails Json?   // Details for the decision, e.g., split amounts
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  escrowOrder     EscrowOrder @relation("EscrowOrderDisputes", fields: [escrowOrderId], references: [id], onDelete: Cascade)
  reporter        User        @relation("EscrowDisputeReporter", fields: [reporterId], references: [id])
  reported        User        @relation("EscrowDisputeReported", fields: [reportedId], references: [id])
  adminUser       User?       @relation("EscrowDisputeAdmin", fields: [adminAssigned], references: [id])
  votes           ArbitrationVote[] @relation("DisputeArbitrationVotes")

  @@index([escrowOrderId])
  @@index([reporterId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
}

model MediatorApplication {
  id                    String   @id @default(cuid())
  userId                String   @unique
  bnbWalletAddress      String
  depositAmount         Float
  feeRate               Float
  experience            String   @db.Text
  introduction          String   @db.Text
  status                String   @default("PENDING") // PENDING, APPROVED, REJECTED, SUSPENDED
  reviewedBy            String?
  reviewNotes           String?  @db.Text
  approvedAt            DateTime?
  rejectedAt            DateTime?
  suspendedAt           DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user      User  @relation("MediatorApplication", fields: [userId], references: [id], onDelete: Cascade)
  reviewer  User? @relation("MediatorApplicationReviewer", fields: [reviewedBy], references: [id])

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model WithdrawalVoucher {
  id          String    @id @default(cuid())
  code        String    @unique
  amount      Float
  description String?
  validUntil  DateTime
  isUsed      Boolean   @default(false)
  usedBy      String?
  usedAt      DateTime?
  issuedBy    String
  issuedAt    DateTime  @default(now())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user   User? @relation("UserWithdrawalVouchers", fields: [usedBy], references: [id])
  issuer User  @relation("IssuedWithdrawalVouchers", fields: [issuedBy], references: [id])

  rewards MediatorReward[] @relation("VoucherRewards")

  @@index([code])
  @@index([isUsed])
  @@index([validUntil])
  @@index([usedBy])
}

model MediatorReward {
  id            String   @id @default(cuid())
  mediatorId    String
  rewardType    String   // VOTING_PARTICIPATION, SUCCESSFUL_MEDIATION, MONTHLY_BONUS
  amount        Float?
  voucherId     String?
  description   String
  earnedAt      DateTime @default(now())
  claimedAt     DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  mediator User               @relation("MediatorRewards", fields: [mediatorId], references: [id])
  voucher  WithdrawalVoucher? @relation("VoucherRewards", fields: [voucherId], references: [id])

  @@index([mediatorId])
  @@index([rewardType])
  @@index([earnedAt])
}

model BlockchainTransaction {
  id              String   @id @default(cuid())
  txHash          String   @unique
  network         String   @default("BNB_CHAIN")
  type            String   @default("ESCROW_FUNDING") // ESCROW_FUNDING, ESCROW_RELEASE, ESCROW_REFUND
  fromAddress     String
  toAddress       String
  amount          Float
  tokenSymbol     String   @default("USDT")
  status          String   @default("PENDING") // PENDING, CONFIRMED, FAILED, VERIFIED, INVALID
  blockNumber     Int?
  gasUsed         Float?
  gasFee          Float?
  confirmations   Int      @default(0)
  relatedOrderId  String?
  relatedEscrowId String?
  metadata        Json?    // 额外的交易元数据
  verifiedAt      DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  order  Order?       @relation("OrderBlockchainTx", fields: [relatedOrderId], references: [id])
  escrow EscrowOrder? @relation("EscrowBlockchainTx", fields: [relatedEscrowId], references: [id])

  @@index([txHash])
  @@index([network])
  @@index([type])
  @@index([status])
  @@index([relatedOrderId])
  @@index([relatedEscrowId])
  @@index([verifiedAt])
}



// 新增：黑名单钱包地址表
model BlacklistedWallet {
  id          String   @id @default(cuid())
  address     String   @unique
  reason      String
  addedBy     String
  addedAt     DateTime @default(now())
  isActive    Boolean  @default(true)
  notes       String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  addedByUser User @relation("BlacklistedWalletAdder", fields: [addedBy], references: [id])

  @@index([address])
  @@index([isActive])
  @@index([addedBy])
}

// 新增：风险评估日志表
model RiskAssessmentLog {
  id               String   @id @default(cuid())
  userId           String
  riskLevel        String   // LOW, MEDIUM, HIGH, CRITICAL
  riskScore        Float
  riskFactors      Json     // 风险因素详情
  requiresApproval Boolean  @default(false)
  requiresKYC      Boolean  @default(false)
  maxAllowedAmount Float?
  recommendations  Json     // 建议列表
  transactionType  String   // 交易类型
  amount           Float
  additionalData   Json?    // 额外数据
  createdAt        DateTime @default(now())

  user User @relation("UserRiskAssessments", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([riskLevel])
  @@index([createdAt])
  @@index([transactionType])
}



// 担保金交易记录表
model GuaranteeTransaction {
  id                String   @id @default(cuid())
  userId            String
  type              String   // DEPOSIT, WITHDRAW, FREEZE, UNFREEZE, EARN, SPEND, TRANSFER
  amount            Float
  balanceBefore     Float    // 交易前余额
  balanceAfter      Float    // 交易后余额
  frozenBefore      Float    // 交易前冻结金额
  frozenAfter       Float    // 交易后冻结金额
  description       String
  relatedType       String?  // ORDER, ESCROW, WITHDRAWAL, DEPOSIT, REWARD
  relatedId         String?  // 关联的订单、托管、提现等ID
  txHash            String?  // 区块链交易哈希
  status            String   @default("COMPLETED") // PENDING, COMPLETED, FAILED, CANCELLED
  metadata          Json?    // 额外元数据
  operatorId        String?  // 操作员ID（如果是管理员操作）
  batchId           String?  // 批次ID（用于批量操作）
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user     User  @relation("UserGuaranteeTransactions", fields: [userId], references: [id], onDelete: Cascade)
  operator User? @relation("OperatorGuaranteeTransactions", fields: [operatorId], references: [id])

  @@index([userId, createdAt])
  @@index([type, createdAt])
  @@index([relatedType, relatedId])
  @@index([status])
  @@index([batchId])
  @@index([txHash])
}

// 担保金等级配置表
model GuaranteeLevel {
  id                    String   @id @default(cuid())
  level                 String   @unique // BRONZE, SILVER, GOLD, DIAMOND, PLATINUM
  minBalance            Float    // 最低余额要求
  maxDailyWithdrawal    Float    // 每日最大提现额度
  withdrawalFeeRate     Float    // 提现手续费率
  tradingFeeDiscount    Float    // 交易手续费折扣
  priorityLevel         Int      @default(0) // 优先级等级
  benefits              Json     // 等级权益详情
  requirements          Json     // 等级要求详情
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@index([level])
  @@index([minBalance])
  @@index([isActive])
}




